module.exports = {

"[project]/.next-internal/server/app/api/search/history/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/pg [external] (pg, esm_import)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("pg");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/lib/database/client.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "closePool": ()=>closePool,
    "getConnection": ()=>getConnection,
    "getPool": ()=>getPool,
    "query": ()=>query,
    "transaction": ()=>transaction
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/pg [external] (pg, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__
]);
[__TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
// 数据库连接配置
const dbConfig = {
    host: process.env.DATABASE_HOST || '*************',
    port: parseInt(process.env.DATABASE_PORT || '5432'),
    database: process.env.DATABASE_NAME || 'prompt',
    user: process.env.DATABASE_USER || 'Seven',
    password: process.env.DATABASE_PASSWORD || 'Abc112211',
    // 连接池配置
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000
};
// 创建连接池
let pool = null;
function getPool() {
    if (!pool) {
        pool = new __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__["Pool"](dbConfig);
        // 监听连接池事件
        pool.on('error', (err)=>{
            console.error('数据库连接池错误:', err);
        });
        pool.on('connect', ()=>{
            console.log('数据库连接成功');
        });
    }
    return pool;
}
async function getConnection() {
    const pool = getPool();
    return await pool.connect();
}
async function query(text, params) {
    const pool = getPool();
    try {
        const result = await pool.query(text, params);
        return result;
    } catch (error) {
        console.error('数据库查询错误:', error);
        throw error;
    }
}
async function transaction(callback) {
    const client = await getConnection();
    try {
        await client.query('BEGIN');
        const result = await callback(client);
        await client.query('COMMIT');
        return result;
    } catch (error) {
        await client.query('ROLLBACK');
        throw error;
    } finally{
        client.release();
    }
}
async function closePool() {
    if (pool) {
        await pool.end();
        pool = null;
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/lib/database/search.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "addSearchHistory": ()=>addSearchHistory,
    "advancedSearch": ()=>advancedSearch,
    "clearSearchHistory": ()=>clearSearchHistory,
    "deleteSearchHistoryItem": ()=>deleteSearchHistoryItem,
    "getPopularSearchTerms": ()=>getPopularSearchTerms,
    "getSearchHistory": ()=>getSearchHistory,
    "getSearchSuggestions": ()=>getSearchSuggestions,
    "searchPrompts": ()=>searchPrompts
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/database/client.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
async function getSearchHistory(limit = 10) {
    try {
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      SELECT * FROM search_history
      ORDER BY updated_at DESC
      LIMIT $1
    `, [
            limit
        ]);
        return result.rows.map((row)=>({
                id: row.id,
                term: row.term,
                count: row.count,
                created_at: row.created_at,
                updated_at: row.updated_at
            }));
    } catch (error) {
        console.error('获取搜索历史失败:', error);
        return [];
    }
}
async function addSearchHistory(searchTerm) {
    try {
        // 检查是否已存在
        const existingResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
      SELECT id, count FROM search_history WHERE term = $1
    `, [
            searchTerm
        ]);
        if (existingResult.rows.length > 0) {
            // 更新计数
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
        UPDATE search_history
        SET count = count + 1, updated_at = NOW()
        WHERE term = $1
      `, [
                searchTerm
            ]);
        } else {
            // 插入新记录
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`
        INSERT INTO search_history (term, count)
        VALUES ($1, 1)
      `, [
                searchTerm
            ]);
        }
    } catch (error) {
        console.error('添加搜索历史失败:', error);
    }
}
async function clearSearchHistory() {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`DELETE FROM search_history`);
        console.log('搜索历史已清除');
    } catch (error) {
        console.error('清除搜索历史失败:', error);
        throw error;
    }
}
async function deleteSearchHistoryItem(id) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(`DELETE FROM search_history WHERE id = $1`, [
            id
        ]);
        console.log('搜索历史记录已删除:', id);
    } catch (error) {
        console.error('删除搜索历史记录失败:', error);
        throw error;
    }
}
async function getPopularSearchTerms(limit = 10) {
    try {
        console.log('获取热门搜索词（搜索历史功能暂未实现）');
        // 当前版本不支持搜索历史功能
        return [];
    } catch (error) {
        console.error('获取热门搜索词失败:', error);
        return [];
    }
}
async function getSearchSuggestions(query, limit = 5) {
    try {
        console.log('获取搜索建议:', query, '（搜索历史功能暂未实现）');
        // 当前版本不支持搜索历史功能
        return [];
    } catch (error) {
        console.error('获取搜索建议失败:', error);
        return [];
    }
}
async function searchPrompts(searchQuery, options = {}) {
    try {
        const { categoryId, tagIds = [], limit = 20, offset = 0 } = options;
        // 记录搜索历史
        if (searchQuery.trim()) {
            await addSearchHistory(searchQuery.trim());
        }
        // 构建基础查询
        let sql = `
      SELECT
        p.*,
        c.name as category_name,
        c.color as category_color,
        c.icon as category_icon,
        COALESCE(
          json_agg(
            json_build_object(
              'id', t.id,
              'name', t.name,
              'color', t.color
            )
          ) FILTER (WHERE t.id IS NOT NULL),
          '[]'::json
        ) as tags
      FROM prompts p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
      LEFT JOIN tags t ON pt.tag_id = t.id
      WHERE p.deleted_at IS NULL
    `;
        const params = [];
        let paramIndex = 1;
        // 全文搜索
        if (searchQuery.trim()) {
            sql += ` AND (
        p.title ILIKE $${paramIndex} OR
        p.description ILIKE $${paramIndex} OR
        p.content ILIKE $${paramIndex}
      )`;
            params.push(`%${searchQuery.trim()}%`);
            paramIndex++;
        }
        // 分类筛选
        if (categoryId) {
            sql += ` AND p.category_id = $${paramIndex}`;
            params.push(categoryId);
            paramIndex++;
        }
        // 标签筛选
        if (tagIds.length > 0) {
            sql += ` AND p.id IN (
        SELECT DISTINCT prompt_id
        FROM prompt_tags
        WHERE tag_id = ANY($${paramIndex})
      )`;
            params.push(tagIds);
            paramIndex++;
        }
        // 分组、排序和分页
        sql += `
      GROUP BY p.id, c.name, c.color, c.icon
      ORDER BY p.updated_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
        params.push(limit, offset);
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(sql, params);
        // 处理数据，确保tags是数组格式
        const processedData = result.rows.map((item)=>({
                ...item,
                category: item.category_name ? {
                    name: item.category_name,
                    color: item.category_color,
                    icon: item.category_icon
                } : null,
                tags: Array.isArray(item.tags) ? item.tags.filter((tag)=>tag.id) : []
            }));
        return processedData;
    } catch (error) {
        console.error('搜索提示词失败:', error);
        throw new Error('搜索提示词失败');
    }
}
async function advancedSearch(params) {
    try {
        return await executeAdvancedSearch(params);
    } catch (error) {
        console.error('高级搜索失败:', error);
        throw new Error('高级搜索失败');
    }
}
async function executeAdvancedSearch(params) {
    try {
        const { query: searchQuery, title, content, categoryId, tagIds = [], dateFrom, dateTo, usageCountMin, usageCountMax, sortBy = 'updated_at', sortOrder = 'desc', limit = 20, offset = 0 } = params;
        // 构建基础查询
        let sql = `
      SELECT
        p.*,
        c.name as category_name,
        c.color as category_color,
        c.icon as category_icon,
        COALESCE(
          json_agg(
            json_build_object(
              'id', t.id,
              'name', t.name,
              'color', t.color
            )
          ) FILTER (WHERE t.id IS NOT NULL),
          '[]'::json
        ) as tags
      FROM prompts p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id
      LEFT JOIN tags t ON pt.tag_id = t.id
      WHERE p.deleted_at IS NULL
    `;
        const queryParams = [];
        let paramIndex = 1;
        // 通用搜索
        if (searchQuery) {
            sql += ` AND (
        p.title ILIKE $${paramIndex} OR
        p.description ILIKE $${paramIndex} OR
        p.content ILIKE $${paramIndex}
      )`;
            queryParams.push(`%${searchQuery}%`);
            paramIndex++;
        }
        // 标题搜索
        if (title) {
            sql += ` AND p.title ILIKE $${paramIndex}`;
            queryParams.push(`%${title}%`);
            paramIndex++;
        }
        // 内容搜索
        if (content) {
            sql += ` AND p.content ILIKE $${paramIndex}`;
            queryParams.push(`%${content}%`);
            paramIndex++;
        }
        // 分类筛选
        if (categoryId) {
            sql += ` AND p.category_id = $${paramIndex}`;
            queryParams.push(categoryId);
            paramIndex++;
        }
        // 标签筛选
        if (tagIds.length > 0) {
            sql += ` AND p.id IN (
        SELECT DISTINCT prompt_id
        FROM prompt_tags
        WHERE tag_id = ANY($${paramIndex})
      )`;
            queryParams.push(tagIds);
            paramIndex++;
        }
        // 日期范围筛选
        if (dateFrom) {
            sql += ` AND p.created_at >= $${paramIndex}`;
            queryParams.push(dateFrom);
            paramIndex++;
        }
        if (dateTo) {
            sql += ` AND p.created_at <= $${paramIndex}`;
            queryParams.push(dateTo);
            paramIndex++;
        }
        // 使用次数筛选
        if (usageCountMin !== undefined) {
            sql += ` AND p.usage_count >= $${paramIndex}`;
            queryParams.push(usageCountMin);
            paramIndex++;
        }
        if (usageCountMax !== undefined) {
            sql += ` AND p.usage_count <= $${paramIndex}`;
            queryParams.push(usageCountMax);
            paramIndex++;
        }
        // 分组、排序和分页
        sql += `
      GROUP BY p.id, c.name, c.color, c.icon
      ORDER BY p.${sortBy} ${sortOrder.toUpperCase()}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
        queryParams.push(limit, offset);
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["query"])(sql, queryParams);
        // 处理数据，确保tags是数组格式
        const processedData = result.rows.map((item)=>({
                ...item,
                category: item.category_name ? {
                    name: item.category_name,
                    color: item.category_color,
                    icon: item.category_icon
                } : null,
                tags: Array.isArray(item.tags) ? item.tags.filter((tag)=>tag.id) : []
            }));
        return processedData;
    } catch (error) {
        console.error('高级搜索执行失败:', error);
        throw new Error('高级搜索执行失败');
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/app/api/search/history/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "DELETE": ()=>DELETE,
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/database/search.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
[__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const limit = parseInt(searchParams.get('limit') || '10');
        const history = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getSearchHistory"])(limit);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(history);
    } catch (error) {
        console.error('获取搜索历史失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '获取搜索历史失败'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const { term } = await request.json();
        if (!term || typeof term !== 'string') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: '搜索词不能为空'
            }, {
                status: 400
            });
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addSearchHistory"])(term.trim());
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true
        });
    } catch (error) {
        console.error('添加搜索历史失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '添加搜索历史失败'
        }, {
            status: 500
        });
    }
}
async function DELETE() {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$database$2f$search$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clearSearchHistory"])();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true
        });
    } catch (error) {
        console.error('清除搜索历史失败:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: '清除搜索历史失败'
        }, {
            status: 500
        });
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__0a67b32e._.js.map