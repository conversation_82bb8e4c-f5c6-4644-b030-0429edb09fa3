{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/database/client.ts"], "sourcesContent": ["import { Pool, PoolClient } from 'pg'\n\n// 数据库连接配置\nconst dbConfig = {\n  host: process.env.DATABASE_HOST || '*************',\n  port: parseInt(process.env.DATABASE_PORT || '5432'),\n  database: process.env.DATABASE_NAME || 'prompt',\n  user: process.env.DATABASE_USER || 'Seven',\n  password: process.env.DATABASE_PASSWORD || 'Abc112211',\n  // 连接池配置\n  max: 20, // 最大连接数\n  idleTimeoutMillis: 30000, // 空闲连接超时时间\n  connectionTimeoutMillis: 2000, // 连接超时时间\n}\n\n// 创建连接池\nlet pool: Pool | null = null\n\n/**\n * 获取数据库连接池\n */\nexport function getPool(): Pool {\n  if (!pool) {\n    pool = new Pool(dbConfig)\n    \n    // 监听连接池事件\n    pool.on('error', (err) => {\n      console.error('数据库连接池错误:', err)\n    })\n    \n    pool.on('connect', () => {\n      console.log('数据库连接成功')\n    })\n  }\n  \n  return pool\n}\n\n/**\n * 获取数据库连接\n */\nexport async function getConnection(): Promise<PoolClient> {\n  const pool = getPool()\n  return await pool.connect()\n}\n\n/**\n * 执行查询\n */\nexport async function query(text: string, params?: any[]): Promise<any> {\n  const pool = getPool()\n  try {\n    const result = await pool.query(text, params)\n    return result\n  } catch (error) {\n    console.error('数据库查询错误:', error)\n    throw error\n  }\n}\n\n/**\n * 执行事务\n */\nexport async function transaction<T>(\n  callback: (client: PoolClient) => Promise<T>\n): Promise<T> {\n  const client = await getConnection()\n  \n  try {\n    await client.query('BEGIN')\n    const result = await callback(client)\n    await client.query('COMMIT')\n    return result\n  } catch (error) {\n    await client.query('ROLLBACK')\n    throw error\n  } finally {\n    client.release()\n  }\n}\n\n/**\n * 关闭连接池\n */\nexport async function closePool(): Promise<void> {\n  if (pool) {\n    await pool.end()\n    pool = null\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;AAEA,UAAU;AACV,MAAM,WAAW;IACf,MAAM,QAAQ,GAAG,CAAC,aAAa,IAAI;IACnC,MAAM,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;IAC5C,UAAU,QAAQ,GAAG,CAAC,aAAa,IAAI;IACvC,MAAM,QAAQ,GAAG,CAAC,aAAa,IAAI;IACnC,UAAU,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IAC3C,QAAQ;IACR,KAAK;IACL,mBAAmB;IACnB,yBAAyB;AAC3B;AAEA,QAAQ;AACR,IAAI,OAAoB;AAKjB,SAAS;IACd,IAAI,CAAC,MAAM;QACT,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;QAEhB,UAAU;QACV,KAAK,EAAE,CAAC,SAAS,CAAC;YAChB,QAAQ,KAAK,CAAC,aAAa;QAC7B;QAEA,KAAK,EAAE,CAAC,WAAW;YACjB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,OAAO;AACT;AAKO,eAAe;IACpB,MAAM,OAAO;IACb,OAAO,MAAM,KAAK,OAAO;AAC3B;AAKO,eAAe,MAAM,IAAY,EAAE,MAAc;IACtD,MAAM,OAAO;IACb,IAAI;QACF,MAAM,SAAS,MAAM,KAAK,KAAK,CAAC,MAAM;QACtC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,MAAM;IACR;AACF;AAKO,eAAe,YACpB,QAA4C;IAE5C,MAAM,SAAS,MAAM;IAErB,IAAI;QACF,MAAM,OAAO,KAAK,CAAC;QACnB,MAAM,SAAS,MAAM,SAAS;QAC9B,MAAM,OAAO,KAAK,CAAC;QACnB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,OAAO,KAAK,CAAC;QACnB,MAAM;IACR,SAAU;QACR,OAAO,OAAO;IAChB;AACF;AAKO,eAAe;IACpB,IAAI,MAAM;QACR,MAAM,KAAK,GAAG;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/database/search.ts"], "sourcesContent": ["import { query } from '@/lib/database/client'\nimport { with<PERSON><PERSON><PERSON><PERSON>, CLIENT_CACHE_KEYS, CLIENT_CACHE_TTL, invalidateClientCache } from '@/lib/client-cache'\nimport type {\n  SearchHistory,\n  SearchHistoryInsert,\n  SearchHistoryUpdate,\n  DatabaseError\n} from '@/types/database'\n\n/**\n * 获取搜索历史\n */\nexport async function getSearchHistory(limit: number = 10): Promise<SearchHistory[]> {\n  try {\n    const result = await query(`\n      SELECT * FROM search_history\n      ORDER BY updated_at DESC\n      LIMIT $1\n    `, [limit])\n\n    return result.rows.map((row: any) => ({\n      id: row.id,\n      term: row.term,\n      count: row.count,\n      created_at: row.created_at,\n      updated_at: row.updated_at\n    }))\n  } catch (error) {\n    console.error('获取搜索历史失败:', error)\n    return []\n  }\n}\n\n/**\n * 添加搜索历史\n */\nexport async function addSearchHistory(searchTerm: string): Promise<void> {\n  try {\n    // 检查是否已存在\n    const existingResult = await query(`\n      SELECT id, count FROM search_history WHERE term = $1\n    `, [searchTerm])\n\n    if (existingResult.rows.length > 0) {\n      // 更新计数\n      await query(`\n        UPDATE search_history\n        SET count = count + 1, updated_at = NOW()\n        WHERE term = $1\n      `, [searchTerm])\n    } else {\n      // 插入新记录\n      await query(`\n        INSERT INTO search_history (term, count)\n        VALUES ($1, 1)\n      `, [searchTerm])\n    }\n  } catch (error) {\n    console.error('添加搜索历史失败:', error)\n  }\n}\n\n/**\n * 清除搜索历史\n */\nexport async function clearSearchHistory(): Promise<void> {\n  try {\n    await query(`DELETE FROM search_history`)\n    console.log('搜索历史已清除')\n  } catch (error) {\n    console.error('清除搜索历史失败:', error)\n    throw error\n  }\n}\n\n/**\n * 删除单个搜索历史记录\n */\nexport async function deleteSearchHistoryItem(id: string): Promise<void> {\n  try {\n    await query(`DELETE FROM search_history WHERE id = $1`, [id])\n    console.log('搜索历史记录已删除:', id)\n  } catch (error) {\n    console.error('删除搜索历史记录失败:', error)\n    throw error\n  }\n}\n\n/**\n * 获取热门搜索词（简化版本）\n */\nexport async function getPopularSearchTerms(limit: number = 10): Promise<SearchHistory[]> {\n  try {\n    console.log('获取热门搜索词（搜索历史功能暂未实现）')\n    // 当前版本不支持搜索历史功能\n    return []\n  } catch (error) {\n    console.error('获取热门搜索词失败:', error)\n    return []\n  }\n}\n\n/**\n * 搜索建议（简化版本）\n */\nexport async function getSearchSuggestions(query: string, limit: number = 5): Promise<string[]> {\n  try {\n    console.log('获取搜索建议:', query, '（搜索历史功能暂未实现）')\n    // 当前版本不支持搜索历史功能\n    return []\n  } catch (error) {\n    console.error('获取搜索建议失败:', error)\n    return []\n  }\n}\n\n/**\n * 全文搜索提示词\n */\nexport async function searchPrompts(\n  searchQuery: string,\n  options: {\n    categoryId?: string\n    tagIds?: string[]\n    limit?: number\n    offset?: number\n  } = {}\n) {\n  try {\n    const { categoryId, tagIds = [], limit = 20, offset = 0 } = options\n\n    // 记录搜索历史\n    if (searchQuery.trim()) {\n      await addSearchHistory(searchQuery.trim())\n    }\n\n    // 构建基础查询\n    let sql = `\n      SELECT\n        p.*,\n        c.name as category_name,\n        c.color as category_color,\n        c.icon as category_icon,\n        COALESCE(\n          json_agg(\n            json_build_object(\n              'id', t.id,\n              'name', t.name,\n              'color', t.color\n            )\n          ) FILTER (WHERE t.id IS NOT NULL),\n          '[]'::json\n        ) as tags\n      FROM prompts p\n      LEFT JOIN categories c ON p.category_id = c.id\n      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id\n      LEFT JOIN tags t ON pt.tag_id = t.id\n      WHERE p.deleted_at IS NULL\n    `\n\n    const params: any[] = []\n    let paramIndex = 1\n\n    // 全文搜索\n    if (searchQuery.trim()) {\n      sql += ` AND (\n        p.title ILIKE $${paramIndex} OR\n        p.description ILIKE $${paramIndex} OR\n        p.content ILIKE $${paramIndex}\n      )`\n      params.push(`%${searchQuery.trim()}%`)\n      paramIndex++\n    }\n\n    // 分类筛选\n    if (categoryId) {\n      sql += ` AND p.category_id = $${paramIndex}`\n      params.push(categoryId)\n      paramIndex++\n    }\n\n    // 标签筛选\n    if (tagIds.length > 0) {\n      sql += ` AND p.id IN (\n        SELECT DISTINCT prompt_id\n        FROM prompt_tags\n        WHERE tag_id = ANY($${paramIndex})\n      )`\n      params.push(tagIds)\n      paramIndex++\n    }\n\n    // 分组、排序和分页\n    sql += `\n      GROUP BY p.id, c.name, c.color, c.icon\n      ORDER BY p.updated_at DESC\n      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}\n    `\n    params.push(limit, offset)\n\n    const result = await query(sql, params)\n\n    // 处理数据，确保tags是数组格式\n    const processedData = result.rows.map(item => ({\n      ...item,\n      category: item.category_name ? {\n        name: item.category_name,\n        color: item.category_color,\n        icon: item.category_icon\n      } : null,\n      tags: Array.isArray(item.tags) ? item.tags.filter(tag => tag.id) : []\n    }))\n\n    return processedData\n  } catch (error) {\n    console.error('搜索提示词失败:', error)\n    throw new Error('搜索提示词失败')\n  }\n}\n\n/**\n * 高级搜索\n */\nexport async function advancedSearch(params: {\n  query?: string\n  title?: string\n  content?: string\n  categoryId?: string\n  tagIds?: string[]\n  dateFrom?: string\n  dateTo?: string\n  usageCountMin?: number\n  usageCountMax?: number\n  sortBy?: 'created_at' | 'updated_at' | 'usage_count' | 'title'\n  sortOrder?: 'asc' | 'desc'\n  limit?: number\n  offset?: number\n}) {\n  try {\n    return await executeAdvancedSearch(params)\n  } catch (error) {\n    console.error('高级搜索失败:', error)\n    throw new Error('高级搜索失败')\n  }\n}\n\nasync function executeAdvancedSearch(params: {\n  query?: string\n  title?: string\n  content?: string\n  categoryId?: string\n  tagIds?: string[]\n  dateFrom?: string\n  dateTo?: string\n  usageCountMin?: number\n  usageCountMax?: number\n  sortBy?: 'created_at' | 'updated_at' | 'usage_count' | 'title'\n  sortOrder?: 'asc' | 'desc'\n  limit?: number\n  offset?: number\n}) {\n  try {\n    const {\n      query: searchQuery,\n      title,\n      content,\n      categoryId,\n      tagIds = [],\n      dateFrom,\n      dateTo,\n      usageCountMin,\n      usageCountMax,\n      sortBy = 'updated_at',\n      sortOrder = 'desc',\n      limit = 20,\n      offset = 0\n    } = params\n\n    // 构建基础查询\n    let sql = `\n      SELECT\n        p.*,\n        c.name as category_name,\n        c.color as category_color,\n        c.icon as category_icon,\n        COALESCE(\n          json_agg(\n            json_build_object(\n              'id', t.id,\n              'name', t.name,\n              'color', t.color\n            )\n          ) FILTER (WHERE t.id IS NOT NULL),\n          '[]'::json\n        ) as tags\n      FROM prompts p\n      LEFT JOIN categories c ON p.category_id = c.id\n      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id\n      LEFT JOIN tags t ON pt.tag_id = t.id\n      WHERE p.deleted_at IS NULL\n    `\n\n    const queryParams: any[] = []\n    let paramIndex = 1\n\n    // 通用搜索\n    if (searchQuery) {\n      sql += ` AND (\n        p.title ILIKE $${paramIndex} OR\n        p.description ILIKE $${paramIndex} OR\n        p.content ILIKE $${paramIndex}\n      )`\n      queryParams.push(`%${searchQuery}%`)\n      paramIndex++\n    }\n\n    // 标题搜索\n    if (title) {\n      sql += ` AND p.title ILIKE $${paramIndex}`\n      queryParams.push(`%${title}%`)\n      paramIndex++\n    }\n\n    // 内容搜索\n    if (content) {\n      sql += ` AND p.content ILIKE $${paramIndex}`\n      queryParams.push(`%${content}%`)\n      paramIndex++\n    }\n\n    // 分类筛选\n    if (categoryId) {\n      sql += ` AND p.category_id = $${paramIndex}`\n      queryParams.push(categoryId)\n      paramIndex++\n    }\n\n    // 标签筛选\n    if (tagIds.length > 0) {\n      sql += ` AND p.id IN (\n        SELECT DISTINCT prompt_id\n        FROM prompt_tags\n        WHERE tag_id = ANY($${paramIndex})\n      )`\n      queryParams.push(tagIds)\n      paramIndex++\n    }\n\n    // 日期范围筛选\n    if (dateFrom) {\n      sql += ` AND p.created_at >= $${paramIndex}`\n      queryParams.push(dateFrom)\n      paramIndex++\n    }\n    if (dateTo) {\n      sql += ` AND p.created_at <= $${paramIndex}`\n      queryParams.push(dateTo)\n      paramIndex++\n    }\n\n    // 使用次数筛选\n    if (usageCountMin !== undefined) {\n      sql += ` AND p.usage_count >= $${paramIndex}`\n      queryParams.push(usageCountMin)\n      paramIndex++\n    }\n    if (usageCountMax !== undefined) {\n      sql += ` AND p.usage_count <= $${paramIndex}`\n      queryParams.push(usageCountMax)\n      paramIndex++\n    }\n\n    // 分组、排序和分页\n    sql += `\n      GROUP BY p.id, c.name, c.color, c.icon\n      ORDER BY p.${sortBy} ${sortOrder.toUpperCase()}\n      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}\n    `\n    queryParams.push(limit, offset)\n\n    const result = await query(sql, queryParams)\n\n    // 处理数据，确保tags是数组格式\n    const processedData = result.rows.map(item => ({\n      ...item,\n      category: item.category_name ? {\n        name: item.category_name,\n        color: item.category_color,\n        icon: item.category_icon\n      } : null,\n      tags: Array.isArray(item.tags) ? item.tags.filter(tag => tag.id) : []\n    }))\n\n    return processedData\n  } catch (error) {\n    console.error('高级搜索执行失败:', error)\n    throw new Error('高级搜索执行失败')\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;AAYO,eAAe,iBAAiB,QAAgB,EAAE;IACvD,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;IAI5B,CAAC,EAAE;YAAC;SAAM;QAEV,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;gBACpC,IAAI,IAAI,EAAE;gBACV,MAAM,IAAI,IAAI;gBACd,OAAO,IAAI,KAAK;gBAChB,YAAY,IAAI,UAAU;gBAC1B,YAAY,IAAI,UAAU;YAC5B,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,EAAE;IACX;AACF;AAKO,eAAe,iBAAiB,UAAkB;IACvD,IAAI;QACF,UAAU;QACV,MAAM,iBAAiB,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;IAEpC,CAAC,EAAE;YAAC;SAAW;QAEf,IAAI,eAAe,IAAI,CAAC,MAAM,GAAG,GAAG;YAClC,OAAO;YACP,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;MAIb,CAAC,EAAE;gBAAC;aAAW;QACjB,OAAO;YACL,QAAQ;YACR,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;MAGb,CAAC,EAAE;gBAAC;aAAW;QACjB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;IAC7B;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC,0BAA0B,CAAC;QACxC,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM;IACR;AACF;AAKO,eAAe,wBAAwB,EAAU;IACtD,IAAI;QACF,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC,wCAAwC,CAAC,EAAE;YAAC;SAAG;QAC5D,QAAQ,GAAG,CAAC,cAAc;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,MAAM;IACR;AACF;AAKO,eAAe,sBAAsB,QAAgB,EAAE;IAC5D,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,gBAAgB;QAChB,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,EAAE;IACX;AACF;AAKO,eAAe,qBAAqB,KAAa,EAAE,QAAgB,CAAC;IACzE,IAAI;QACF,QAAQ,GAAG,CAAC,WAAW,OAAO;QAC9B,gBAAgB;QAChB,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,EAAE;IACX;AACF;AAKO,eAAe,cACpB,WAAmB,EACnB,UAKI,CAAC,CAAC;IAEN,IAAI;QACF,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,EAAE,GAAG;QAE5D,SAAS;QACT,IAAI,YAAY,IAAI,IAAI;YACtB,MAAM,iBAAiB,YAAY,IAAI;QACzC;QAEA,SAAS;QACT,IAAI,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;IAqBX,CAAC;QAED,MAAM,SAAgB,EAAE;QACxB,IAAI,aAAa;QAEjB,OAAO;QACP,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,CAAC;uBACS,EAAE,WAAW;6BACP,EAAE,WAAW;yBACjB,EAAE,WAAW;OAC/B,CAAC;YACF,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,IAAI,GAAG,CAAC,CAAC;YACrC;QACF;QAEA,OAAO;QACP,IAAI,YAAY;YACd,OAAO,CAAC,sBAAsB,EAAE,YAAY;YAC5C,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,OAAO;QACP,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,OAAO,CAAC;;;4BAGc,EAAE,WAAW;OAClC,CAAC;YACF,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,WAAW;QACX,OAAO,CAAC;;;aAGC,EAAE,WAAW,SAAS,EAAE,aAAa,EAAE;IAChD,CAAC;QACD,OAAO,IAAI,CAAC,OAAO;QAEnB,MAAM,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,KAAK;QAEhC,mBAAmB;QACnB,MAAM,gBAAgB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC7C,GAAG,IAAI;gBACP,UAAU,KAAK,aAAa,GAAG;oBAC7B,MAAM,KAAK,aAAa;oBACxB,OAAO,KAAK,cAAc;oBAC1B,MAAM,KAAK,aAAa;gBAC1B,IAAI;gBACJ,MAAM,MAAM,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,IAAI,EAAE;YACvE,CAAC;QAED,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,eAAe,MAcpC;IACC,IAAI;QACF,OAAO,MAAM,sBAAsB;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,eAAe,sBAAsB,MAcpC;IACC,IAAI;QACF,MAAM,EACJ,OAAO,WAAW,EAClB,KAAK,EACL,OAAO,EACP,UAAU,EACV,SAAS,EAAE,EACX,QAAQ,EACR,MAAM,EACN,aAAa,EACb,aAAa,EACb,SAAS,YAAY,EACrB,YAAY,MAAM,EAClB,QAAQ,EAAE,EACV,SAAS,CAAC,EACX,GAAG;QAEJ,SAAS;QACT,IAAI,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;IAqBX,CAAC;QAED,MAAM,cAAqB,EAAE;QAC7B,IAAI,aAAa;QAEjB,OAAO;QACP,IAAI,aAAa;YACf,OAAO,CAAC;uBACS,EAAE,WAAW;6BACP,EAAE,WAAW;yBACjB,EAAE,WAAW;OAC/B,CAAC;YACF,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;YACnC;QACF;QAEA,OAAO;QACP,IAAI,OAAO;YACT,OAAO,CAAC,oBAAoB,EAAE,YAAY;YAC1C,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAC7B;QACF;QAEA,OAAO;QACP,IAAI,SAAS;YACX,OAAO,CAAC,sBAAsB,EAAE,YAAY;YAC5C,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;YAC/B;QACF;QAEA,OAAO;QACP,IAAI,YAAY;YACd,OAAO,CAAC,sBAAsB,EAAE,YAAY;YAC5C,YAAY,IAAI,CAAC;YACjB;QACF;QAEA,OAAO;QACP,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,OAAO,CAAC;;;4BAGc,EAAE,WAAW;OAClC,CAAC;YACF,YAAY,IAAI,CAAC;YACjB;QACF;QAEA,SAAS;QACT,IAAI,UAAU;YACZ,OAAO,CAAC,sBAAsB,EAAE,YAAY;YAC5C,YAAY,IAAI,CAAC;YACjB;QACF;QACA,IAAI,QAAQ;YACV,OAAO,CAAC,sBAAsB,EAAE,YAAY;YAC5C,YAAY,IAAI,CAAC;YACjB;QACF;QAEA,SAAS;QACT,IAAI,kBAAkB,WAAW;YAC/B,OAAO,CAAC,uBAAuB,EAAE,YAAY;YAC7C,YAAY,IAAI,CAAC;YACjB;QACF;QACA,IAAI,kBAAkB,WAAW;YAC/B,OAAO,CAAC,uBAAuB,EAAE,YAAY;YAC7C,YAAY,IAAI,CAAC;YACjB;QACF;QAEA,WAAW;QACX,OAAO,CAAC;;iBAEK,EAAE,OAAO,CAAC,EAAE,UAAU,WAAW,GAAG;aACxC,EAAE,WAAW,SAAS,EAAE,aAAa,EAAE;IAChD,CAAC;QACD,YAAY,IAAI,CAAC,OAAO;QAExB,MAAM,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,KAAK;QAEhC,mBAAmB;QACnB,MAAM,gBAAgB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC7C,GAAG,IAAI;gBACP,UAAU,KAAK,aAAa,GAAG;oBAC7B,MAAM,KAAK,aAAa;oBACxB,OAAO,KAAK,cAAc;oBAC1B,MAAM,KAAK,aAAa;gBAC1B,IAAI;gBACJ,MAAM,MAAM,OAAO,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,IAAI,EAAE;YACvE,CAAC;QAED,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/app/api/search/history/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { \n  getSearchHistory, \n  addSearchHistory, \n  clearSearchHistory \n} from '@/lib/database/search'\n\n// GET /api/search/history - 获取搜索历史\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const limit = parseInt(searchParams.get('limit') || '10')\n\n    const history = await getSearchHistory(limit)\n    return NextResponse.json(history)\n  } catch (error) {\n    console.error('获取搜索历史失败:', error)\n    return NextResponse.json(\n      { error: '获取搜索历史失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST /api/search/history - 添加搜索历史\nexport async function POST(request: NextRequest) {\n  try {\n    const { term } = await request.json()\n    \n    if (!term || typeof term !== 'string') {\n      return NextResponse.json(\n        { error: '搜索词不能为空' },\n        { status: 400 }\n      )\n    }\n\n    await addSearchHistory(term.trim())\n    return NextResponse.json({ success: true })\n  } catch (error) {\n    console.error('添加搜索历史失败:', error)\n    return NextResponse.json(\n      { error: '添加搜索历史失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE /api/search/history - 清除搜索历史\nexport async function DELETE() {\n  try {\n    await clearSearchHistory()\n    return NextResponse.json({ success: true })\n  } catch (error) {\n    console.error('清除搜索历史失败:', error)\n    return NextResponse.json(\n      { error: '清除搜索历史失败' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;;;;AAOO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAEpD,MAAM,UAAU,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEnC,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,IAAI;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,CAAA,GAAA,2HAAA,CAAA,qBAAkB,AAAD;QACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}