{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/database/client.ts"], "sourcesContent": ["import { Pool, PoolClient } from 'pg'\n\n// 数据库连接配置\nconst dbConfig = {\n  host: process.env.DATABASE_HOST || '*************',\n  port: parseInt(process.env.DATABASE_PORT || '5432'),\n  database: process.env.DATABASE_NAME || 'prompt',\n  user: process.env.DATABASE_USER || 'Seven',\n  password: process.env.DATABASE_PASSWORD || 'Abc112211',\n  // 连接池配置\n  max: 20, // 最大连接数\n  idleTimeoutMillis: 30000, // 空闲连接超时时间\n  connectionTimeoutMillis: 2000, // 连接超时时间\n}\n\n// 创建连接池\nlet pool: Pool | null = null\n\n/**\n * 获取数据库连接池\n */\nexport function getPool(): Pool {\n  if (!pool) {\n    pool = new Pool(dbConfig)\n    \n    // 监听连接池事件\n    pool.on('error', (err) => {\n      console.error('数据库连接池错误:', err)\n    })\n    \n    pool.on('connect', () => {\n      console.log('数据库连接成功')\n    })\n  }\n  \n  return pool\n}\n\n/**\n * 获取数据库连接\n */\nexport async function getConnection(): Promise<PoolClient> {\n  const pool = getPool()\n  return await pool.connect()\n}\n\n/**\n * 执行查询\n */\nexport async function query(text: string, params?: any[]): Promise<any> {\n  const pool = getPool()\n  try {\n    const result = await pool.query(text, params)\n    return result\n  } catch (error) {\n    console.error('数据库查询错误:', error)\n    throw error\n  }\n}\n\n/**\n * 执行事务\n */\nexport async function transaction<T>(\n  callback: (client: PoolClient) => Promise<T>\n): Promise<T> {\n  const client = await getConnection()\n  \n  try {\n    await client.query('BEGIN')\n    const result = await callback(client)\n    await client.query('COMMIT')\n    return result\n  } catch (error) {\n    await client.query('ROLLBACK')\n    throw error\n  } finally {\n    client.release()\n  }\n}\n\n/**\n * 关闭连接池\n */\nexport async function closePool(): Promise<void> {\n  if (pool) {\n    await pool.end()\n    pool = null\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;AAEA,UAAU;AACV,MAAM,WAAW;IACf,MAAM,QAAQ,GAAG,CAAC,aAAa,IAAI;IACnC,MAAM,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;IAC5C,UAAU,QAAQ,GAAG,CAAC,aAAa,IAAI;IACvC,MAAM,QAAQ,GAAG,CAAC,aAAa,IAAI;IACnC,UAAU,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IAC3C,QAAQ;IACR,KAAK;IACL,mBAAmB;IACnB,yBAAyB;AAC3B;AAEA,QAAQ;AACR,IAAI,OAAoB;AAKjB,SAAS;IACd,IAAI,CAAC,MAAM;QACT,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;QAEhB,UAAU;QACV,KAAK,EAAE,CAAC,SAAS,CAAC;YAChB,QAAQ,KAAK,CAAC,aAAa;QAC7B;QAEA,KAAK,EAAE,CAAC,WAAW;YACjB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,OAAO;AACT;AAKO,eAAe;IACpB,MAAM,OAAO;IACb,OAAO,MAAM,KAAK,OAAO;AAC3B;AAKO,eAAe,MAAM,IAAY,EAAE,MAAc;IACtD,MAAM,OAAO;IACb,IAAI;QACF,MAAM,SAAS,MAAM,KAAK,KAAK,CAAC,MAAM;QACtC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,MAAM;IACR;AACF;AAKO,eAAe,YACpB,QAA4C;IAE5C,MAAM,SAAS,MAAM;IAErB,IAAI;QACF,MAAM,OAAO,KAAK,CAAC;QACnB,MAAM,SAAS,MAAM,SAAS;QAC9B,MAAM,OAAO,KAAK,CAAC;QACnB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,OAAO,KAAK,CAAC;QACnB,MAAM;IACR,SAAU;QACR,OAAO,OAAO;IAChB;AACF;AAKO,eAAe;IACpB,IAAI,MAAM;QACR,MAAM,KAAK,GAAG;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/client-cache.ts"], "sourcesContent": ["/**\n * 浏览器端缓存系统\n * 解决服务器端内存缓存在 Vercel 等 serverless 环境中的问题\n */\n\ninterface CacheItem<T> {\n  data: T\n  timestamp: number\n  ttl: number\n  version: string\n}\n\nclass ClientCache {\n  private memoryCache = new Map<string, CacheItem<any>>()\n  private readonly version = '1.0.0' // 缓存版本，用于缓存失效\n\n  /**\n   * 生成稳定的缓存键\n   */\n  private generateKey(key: string, params?: any): string {\n    if (!params) return key\n    \n    // 确保对象键的顺序一致\n    const sortedParams = this.sortObject(params)\n    return `${key}:${JSON.stringify(sortedParams)}`\n  }\n\n  /**\n   * 递归排序对象键，确保缓存键的一致性\n   */\n  private sortObject(obj: any): any {\n    if (obj === null || typeof obj !== 'object') return obj\n    if (Array.isArray(obj)) return obj.map(item => this.sortObject(item))\n    \n    const sorted: any = {}\n    Object.keys(obj).sort().forEach(key => {\n      sorted[key] = this.sortObject(obj[key])\n    })\n    return sorted\n  }\n\n  /**\n   * 设置缓存（内存 + localStorage）\n   */\n  set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000, params?: any): void {\n    const cacheKey = this.generateKey(key, params)\n    const item: CacheItem<T> = {\n      data,\n      timestamp: Date.now(),\n      ttl,\n      version: this.version\n    }\n\n    // 内存缓存\n    this.memoryCache.set(cacheKey, item)\n\n    // localStorage 缓存（仅在浏览器环境）\n    if (typeof window !== 'undefined') {\n      try {\n        localStorage.setItem(`cache_${cacheKey}`, JSON.stringify(item))\n      } catch (error) {\n        console.warn('localStorage 缓存失败:', error)\n      }\n    }\n  }\n\n  /**\n   * 获取缓存\n   */\n  get<T>(key: string, params?: any): T | null {\n    const cacheKey = this.generateKey(key, params)\n\n    // 先尝试内存缓存\n    let item = this.memoryCache.get(cacheKey)\n\n    // 如果内存缓存没有，尝试 localStorage\n    if (!item && typeof window !== 'undefined') {\n      try {\n        const stored = localStorage.getItem(`cache_${cacheKey}`)\n        if (stored) {\n          item = JSON.parse(stored)\n          // 恢复到内存缓存\n          if (item) {\n            this.memoryCache.set(cacheKey, item)\n          }\n        }\n      } catch (error) {\n        console.warn('localStorage 读取失败:', error)\n      }\n    }\n\n    if (!item) return null\n\n    // 检查版本\n    if (item.version !== this.version) {\n      this.delete(key, params)\n      return null\n    }\n\n    // 检查是否过期\n    if (Date.now() - item.timestamp > item.ttl) {\n      this.delete(key, params)\n      return null\n    }\n\n    return item.data as T\n  }\n\n  /**\n   * 删除缓存\n   */\n  delete(key: string, params?: any): void {\n    const cacheKey = this.generateKey(key, params)\n    \n    this.memoryCache.delete(cacheKey)\n    \n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(`cache_${cacheKey}`)\n    }\n  }\n\n  /**\n   * 清除所有缓存\n   */\n  clear(): void {\n    this.memoryCache.clear()\n    \n    if (typeof window !== 'undefined') {\n      const keys = Object.keys(localStorage)\n      keys.forEach(key => {\n        if (key.startsWith('cache_')) {\n          localStorage.removeItem(key)\n        }\n      })\n    }\n  }\n\n  /**\n   * 清除匹配模式的缓存\n   */\n  clearPattern(pattern: string): void {\n    // 清除内存缓存\n    for (const key of this.memoryCache.keys()) {\n      if (key.includes(pattern)) {\n        this.memoryCache.delete(key)\n      }\n    }\n\n    // 清除 localStorage 缓存\n    if (typeof window !== 'undefined') {\n      const keys = Object.keys(localStorage)\n      keys.forEach(key => {\n        if (key.startsWith('cache_') && key.includes(pattern)) {\n          localStorage.removeItem(key)\n        }\n      })\n    }\n  }\n\n  /**\n   * 获取缓存统计信息\n   */\n  getStats(): { memorySize: number; localStorageSize: number; keys: string[] } {\n    let localStorageSize = 0\n    const keys: string[] = []\n\n    if (typeof window !== 'undefined') {\n      Object.keys(localStorage).forEach(key => {\n        if (key.startsWith('cache_')) {\n          localStorageSize++\n          keys.push(key.replace('cache_', ''))\n        }\n      })\n    }\n\n    return {\n      memorySize: this.memoryCache.size,\n      localStorageSize,\n      keys: Array.from(new Set([...Array.from(this.memoryCache.keys()), ...keys]))\n    }\n  }\n}\n\n// 创建全局客户端缓存实例\nexport const clientCache = new ClientCache()\n\n// 缓存键常量\nexport const CLIENT_CACHE_KEYS = {\n  CATEGORIES: 'categories',\n  PROMPTS: 'prompts',\n  TAGS: 'tags',\n  SEARCH_HISTORY: 'search_history',\n  PROMPT_DETAIL: 'prompt_detail',\n  APP_PREFERENCES: 'app_preferences',\n} as const\n\n// 缓存时间常量（毫秒）\nexport const CLIENT_CACHE_TTL = {\n  SHORT: 30 * 1000,        // 30秒\n  MEDIUM: 2 * 60 * 1000,   // 2分钟\n  LONG: 10 * 60 * 1000,    // 10分钟\n  VERY_LONG: 30 * 60 * 1000, // 30分钟\n} as const\n\n/**\n * 客户端缓存装饰器函数\n */\nexport async function withClientCache<T>(\n  cacheKey: string,\n  ttl: number,\n  fn: () => Promise<T>,\n  params?: any\n): Promise<T> {\n  // 尝试从缓存获取\n  const cached = clientCache.get<T>(cacheKey, params)\n  if (cached !== null) {\n    console.log(`Client cache hit: ${cacheKey}`, params)\n    return cached\n  }\n\n  // 缓存未命中，执行函数\n  console.log(`Client cache miss: ${cacheKey}`, params)\n  const result = await fn()\n  \n  // 存入缓存\n  clientCache.set(cacheKey, result, ttl, params)\n  \n  return result\n}\n\n/**\n * 清除相关缓存\n */\nexport function invalidateClientCache(patterns: string[]): void {\n  patterns.forEach(pattern => {\n    clientCache.clearPattern(pattern)\n  })\n  console.log(`Invalidated client cache patterns: ${patterns.join(', ')}`)\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AASD,MAAM;IACI,cAAc,IAAI,MAA6B;IACtC,UAAU,QAAQ,cAAc;KAAf;IAElC;;GAEC,GACD,AAAQ,YAAY,GAAW,EAAE,MAAY,EAAU;QACrD,IAAI,CAAC,QAAQ,OAAO;QAEpB,aAAa;QACb,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC;QACrC,OAAO,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,eAAe;IACjD;IAEA;;GAEC,GACD,AAAQ,WAAW,GAAQ,EAAO;QAChC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU,OAAO;QACpD,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,UAAU,CAAC;QAE/D,MAAM,SAAc,CAAC;QACrB,OAAO,IAAI,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,CAAA;YAC9B,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI;QACxC;QACA,OAAO;IACT;IAEA;;GAEC,GACD,IAAO,GAAW,EAAE,IAAO,EAAE,MAAc,IAAI,KAAK,IAAI,EAAE,MAAY,EAAQ;QAC5E,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,KAAK;QACvC,MAAM,OAAqB;YACzB;YACA,WAAW,KAAK,GAAG;YACnB;YACA,SAAS,IAAI,CAAC,OAAO;QACvB;QAEA,OAAO;QACP,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU;QAE/B,2BAA2B;QAC3B;;IAOF;IAEA;;GAEC,GACD,IAAO,GAAW,EAAE,MAAY,EAAY;QAC1C,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,KAAK;QAEvC,UAAU;QACV,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QAEhC,2BAA2B;QAC3B;;QAeA,IAAI,CAAC,MAAM,OAAO;QAElB,OAAO;QACP,IAAI,KAAK,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;YACjC,IAAI,CAAC,MAAM,CAAC,KAAK;YACjB,OAAO;QACT;QAEA,SAAS;QACT,IAAI,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,KAAK,GAAG,EAAE;YAC1C,IAAI,CAAC,MAAM,CAAC,KAAK;YACjB,OAAO;QACT;QAEA,OAAO,KAAK,IAAI;IAClB;IAEA;;GAEC,GACD,OAAO,GAAW,EAAE,MAAY,EAAQ;QACtC,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,KAAK;QAEvC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QAExB;;IAGF;IAEA;;GAEC,GACD,QAAc;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK;QAEtB;;IAQF;IAEA;;GAEC,GACD,aAAa,OAAe,EAAQ;QAClC,SAAS;QACT,KAAK,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,GAAI;YACzC,IAAI,IAAI,QAAQ,CAAC,UAAU;gBACzB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YAC1B;QACF;QAEA,qBAAqB;QACrB;;IAQF;IAEA;;GAEC,GACD,WAA6E;QAC3E,IAAI,mBAAmB;QACvB,MAAM,OAAiB,EAAE;QAEzB;;QASA,OAAO;YACL,YAAY,IAAI,CAAC,WAAW,CAAC,IAAI;YACjC;YACA,MAAM,MAAM,IAAI,CAAC,IAAI,IAAI;mBAAI,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI;mBAAQ;aAAK;QAC5E;IACF;AACF;AAGO,MAAM,cAAc,IAAI;AAGxB,MAAM,oBAAoB;IAC/B,YAAY;IACZ,SAAS;IACT,MAAM;IACN,gBAAgB;IAChB,eAAe;IACf,iBAAiB;AACnB;AAGO,MAAM,mBAAmB;IAC9B,OAAO,KAAK;IACZ,QAAQ,IAAI,KAAK;IACjB,MAAM,KAAK,KAAK;IAChB,WAAW,KAAK,KAAK;AACvB;AAKO,eAAe,gBACpB,QAAgB,EAChB,GAAW,EACX,EAAoB,EACpB,MAAY;IAEZ,UAAU;IACV,MAAM,SAAS,YAAY,GAAG,CAAI,UAAU;IAC5C,IAAI,WAAW,MAAM;QACnB,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU,EAAE;QAC7C,OAAO;IACT;IAEA,aAAa;IACb,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU,EAAE;IAC9C,MAAM,SAAS,MAAM;IAErB,OAAO;IACP,YAAY,GAAG,CAAC,UAAU,QAAQ,KAAK;IAEvC,OAAO;AACT;AAKO,SAAS,sBAAsB,QAAkB;IACtD,SAAS,OAAO,CAAC,CAAA;QACf,YAAY,YAAY,CAAC;IAC3B;IACA,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,SAAS,IAAI,CAAC,OAAO;AACzE", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/database/prompts.ts"], "sourcesContent": ["import { query, transaction } from '@/lib/database/client'\nimport { withClientCache, CLIENT_CACHE_KEYS, CLIENT_CACHE_TTL, invalidateClientCache } from '@/lib/client-cache'\nimport type {\n  Prompt,\n  PromptInsert,\n  PromptUpdate,\n  PromptWithDetails,\n  SearchParams,\n  PaginatedResponse,\n  DatabaseError\n} from '@/types/database'\n\n/**\n * 获取提示词列表（支持搜索和分页）\n */\nexport async function getPrompts(params: SearchParams = {}): Promise<PaginatedResponse<PromptWithDetails>> {\n  const {\n    query: searchQuery,\n    categoryId,\n    tagIds = [],\n    sortBy = 'updated_at',\n    sortOrder = 'desc',\n    limit = 12,\n    offset = 0\n  } = params\n\n  // 只对非搜索查询进行缓存（搜索结果变化频繁）\n  const shouldCache = !searchQuery && offset === 0 && limit <= 12\n\n  if (shouldCache) {\n    return withClientCache(\n      CLIENT_CACHE_KEYS.PROMPTS,\n      CLIENT_CACHE_TTL.MEDIUM,\n      () => executeQuery(params),\n      params\n    )\n  } else {\n    return executeQuery(params)\n  }\n}\n\nasync function executeQuery(params: SearchParams): Promise<PaginatedResponse<PromptWithDetails>> {\n  const {\n    query: searchQuery,\n    categoryId,\n    tagIds = [],\n    sortBy = 'updated_at',\n    sortOrder = 'desc',\n    limit = 12,\n    offset = 0\n  } = params\n\n  try {\n    // 构建基础查询\n    let queryText = `\n      SELECT \n        p.id,\n        p.title,\n        p.description,\n        p.content,\n        p.category_id,\n        p.usage_count,\n        p.metadata,\n        p.created_at,\n        p.updated_at,\n        c.name as category_name,\n        c.color as category_color,\n        c.icon as category_icon,\n        COALESCE(\n          ARRAY_AGG(t.name) FILTER (WHERE t.name IS NOT NULL),\n          ARRAY[]::TEXT[]\n        ) as tag_names,\n        COALESCE(\n          ARRAY_AGG(t.id) FILTER (WHERE t.id IS NOT NULL),\n          ARRAY[]::UUID[]\n        ) as tag_ids,\n        COALESCE(\n          ARRAY_AGG(t.color) FILTER (WHERE t.color IS NOT NULL),\n          ARRAY[]::TEXT[]\n        ) as tag_colors\n      FROM prompts p\n      LEFT JOIN categories c ON p.category_id = c.id\n      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id\n      LEFT JOIN tags t ON pt.tag_id = t.id\n      WHERE p.deleted_at IS NULL\n    `\n\n    const queryParams: any[] = []\n    let paramIndex = 1\n\n    // 添加分类过滤\n    if (categoryId) {\n      queryText += ` AND p.category_id = $${paramIndex}`\n      queryParams.push(categoryId)\n      paramIndex++\n    }\n\n    // 添加搜索条件\n    if (searchQuery) {\n      queryText += ` AND (\n        p.title ILIKE $${paramIndex} \n        OR p.content ILIKE $${paramIndex}\n        OR to_tsvector('english', p.title || ' ' || p.content) @@ plainto_tsquery('english', $${paramIndex + 1})\n      )`\n      queryParams.push(`%${searchQuery}%`, searchQuery)\n      paramIndex += 2\n    }\n\n    // 添加标签过滤\n    if (tagIds.length > 0) {\n      queryText += ` AND p.id IN (\n        SELECT pt.prompt_id \n        FROM prompt_tags pt \n        WHERE pt.tag_id = ANY($${paramIndex})\n      )`\n      queryParams.push(tagIds)\n      paramIndex++\n    }\n\n    queryText += ` GROUP BY p.id, c.name, c.color, c.icon`\n\n    // 添加排序\n    const validSortColumns = ['created_at', 'updated_at', 'usage_count', 'title']\n    const validSortOrders = ['asc', 'desc']\n    \n    if (validSortColumns.includes(sortBy) && validSortOrders.includes(sortOrder)) {\n      queryText += ` ORDER BY p.${sortBy} ${sortOrder.toUpperCase()}`\n    } else {\n      queryText += ` ORDER BY p.updated_at DESC`\n    }\n\n    // 添加分页\n    queryText += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`\n    queryParams.push(limit, offset)\n\n    // 执行查询\n    const result = await query(queryText, queryParams)\n\n    // 获取总数\n    let countQuery = `\n      SELECT COUNT(DISTINCT p.id) as total\n      FROM prompts p\n      LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id\n      WHERE p.deleted_at IS NULL\n    `\n    \n    const countParams: any[] = []\n    let countParamIndex = 1\n\n    if (categoryId) {\n      countQuery += ` AND p.category_id = $${countParamIndex}`\n      countParams.push(categoryId)\n      countParamIndex++\n    }\n\n    if (searchQuery) {\n      countQuery += ` AND (\n        p.title ILIKE $${countParamIndex} \n        OR p.content ILIKE $${countParamIndex}\n        OR to_tsvector('english', p.title || ' ' || p.content) @@ plainto_tsquery('english', $${countParamIndex + 1})\n      )`\n      countParams.push(`%${searchQuery}%`, searchQuery)\n      countParamIndex += 2\n    }\n\n    if (tagIds.length > 0) {\n      countQuery += ` AND p.id IN (\n        SELECT pt.prompt_id \n        FROM prompt_tags pt \n        WHERE pt.tag_id = ANY($${countParamIndex})\n      )`\n      countParams.push(tagIds)\n    }\n\n    const countResult = await query(countQuery, countParams)\n    const total = parseInt(countResult.rows[0].total)\n\n    // 处理结果数据\n    const prompts = result.rows.map((row: any) => ({\n      id: row.id,\n      title: row.title,\n      description: row.description,\n      content: row.content,\n      category_id: row.category_id,\n      usage_count: row.usage_count,\n      user_id: row.user_id,\n      metadata: row.metadata,\n      created_at: row.created_at,\n      updated_at: row.updated_at,\n      deleted_at: row.deleted_at,\n      category: row.category_name ? {\n        id: row.category_id,\n        name: row.category_name,\n        color: row.category_color,\n        icon: row.category_icon\n      } : null,\n      tags: row.tag_names.map((name: string, index: number) => ({\n        id: row.tag_ids[index],\n        name,\n        color: row.tag_colors[index]\n      })).filter((tag: any) => tag.name)\n    }))\n\n    return {\n      data: prompts,\n      total,\n      hasMore: offset + limit < total,\n      nextOffset: offset + limit < total ? offset + limit : null\n    }\n\n  } catch (error) {\n    console.error('获取提示词列表失败:', error)\n    throw new Error('获取提示词列表失败')\n  }\n}\n\n/**\n * 根据ID获取单个提示词（带缓存）\n */\nexport async function getPromptById(id: string): Promise<PromptWithDetails | null> {\n  try {\n    // 如果是本地ID，不要查询数据库\n    if (id.startsWith('local_')) {\n      console.warn('尝试查询本地ID，跳过查询:', id)\n      return null\n    }\n\n    // 使用客户端缓存，缓存时间为5分钟\n    return withClientCache(\n      `${CLIENT_CACHE_KEYS.PROMPT_DETAIL}_${id}`,\n      CLIENT_CACHE_TTL.MEDIUM,\n      async () => {\n        const result = await query(`\n          SELECT \n            p.id,\n            p.title,\n            p.description,\n            p.content,\n            p.category_id,\n            p.usage_count,\n            p.metadata,\n            p.created_at,\n            p.updated_at,\n            c.name as category_name,\n            c.color as category_color,\n            c.icon as category_icon,\n            COALESCE(\n              ARRAY_AGG(t.name) FILTER (WHERE t.name IS NOT NULL),\n              ARRAY[]::TEXT[]\n            ) as tag_names,\n            COALESCE(\n              ARRAY_AGG(t.id) FILTER (WHERE t.id IS NOT NULL),\n              ARRAY[]::UUID[]\n            ) as tag_ids,\n            COALESCE(\n              ARRAY_AGG(t.color) FILTER (WHERE t.color IS NOT NULL),\n              ARRAY[]::TEXT[]\n            ) as tag_colors\n          FROM prompts p\n          LEFT JOIN categories c ON p.category_id = c.id\n          LEFT JOIN prompt_tags pt ON p.id = pt.prompt_id\n          LEFT JOIN tags t ON pt.tag_id = t.id\n          WHERE p.id = $1 AND p.deleted_at IS NULL\n          GROUP BY p.id, c.name, c.color, c.icon\n        `, [id])\n\n        if (result.rows.length === 0) {\n          return null\n        }\n\n        const row = result.rows[0]\n        return {\n          id: row.id,\n          title: row.title,\n          description: row.description,\n          content: row.content,\n          category_id: row.category_id,\n          usage_count: row.usage_count,\n          user_id: row.user_id,\n          metadata: row.metadata,\n          created_at: row.created_at,\n          updated_at: row.updated_at,\n          deleted_at: row.deleted_at,\n          category: row.category_name ? {\n            id: row.category_id,\n            name: row.category_name,\n            color: row.category_color,\n            icon: row.category_icon\n          } : null,\n          tags: row.tag_names.map((name: string, index: number) => ({\n            id: row.tag_ids[index],\n            name,\n            color: row.tag_colors[index]\n          })).filter((tag: any) => tag.name)\n        }\n      }\n    )\n  } catch (error) {\n    console.error('获取提示词失败:', error)\n    throw new Error('获取提示词失败')\n  }\n}\n\n/**\n * 创建新提示词\n */\nexport async function createPrompt(data: PromptInsert): Promise<PromptWithDetails> {\n  try {\n    return await transaction(async (client) => {\n      // 插入提示词（使用默认user_id，因为用户认证已移除）\n      const defaultUserId = 'default-user'\n      const promptResult = await client.query(`\n        INSERT INTO prompts (title, description, content, category_id, user_id, metadata)\n        VALUES ($1, $2, $3, $4, $5, $6)\n        RETURNING *\n      `, [data.title, data.description, data.content, data.category_id, data.user_id || defaultUserId, data.metadata || {}])\n\n      const prompt = promptResult.rows[0]\n\n      // 处理标签关联\n      if (data.tagIds && data.tagIds.length > 0) {\n        for (const tagId of data.tagIds) {\n          await client.query(`\n            INSERT INTO prompt_tags (prompt_id, tag_id)\n            VALUES ($1, $2)\n            ON CONFLICT (prompt_id, tag_id) DO NOTHING\n          `, [prompt.id, tagId])\n        }\n      }\n\n      // 清除缓存\n      invalidateClientCache([CLIENT_CACHE_KEYS.PROMPTS])\n\n      // 获取完整的提示词信息\n      return await getPromptById(prompt.id) as PromptWithDetails\n    })\n  } catch (error) {\n    console.error('创建提示词失败:', error)\n    throw new Error('创建提示词失败')\n  }\n}\n\n/**\n * 更新提示词\n */\nexport async function updatePrompt(id: string, data: PromptUpdate): Promise<PromptWithDetails> {\n  try {\n    return await transaction(async (client) => {\n      // 更新提示词基本信息\n      const updateFields: string[] = []\n      const updateValues: any[] = []\n      let paramIndex = 1\n\n      if (data.title !== undefined) {\n        updateFields.push(`title = $${paramIndex}`)\n        updateValues.push(data.title)\n        paramIndex++\n      }\n\n      if (data.description !== undefined) {\n        updateFields.push(`description = $${paramIndex}`)\n        updateValues.push(data.description)\n        paramIndex++\n      }\n\n      if (data.content !== undefined) {\n        updateFields.push(`content = $${paramIndex}`)\n        updateValues.push(data.content)\n        paramIndex++\n      }\n\n      if (data.category_id !== undefined) {\n        updateFields.push(`category_id = $${paramIndex}`)\n        updateValues.push(data.category_id)\n        paramIndex++\n      }\n\n      if (data.metadata !== undefined) {\n        updateFields.push(`metadata = $${paramIndex}`)\n        updateValues.push(data.metadata)\n        paramIndex++\n      }\n\n      if (updateFields.length > 0) {\n        updateFields.push(`updated_at = NOW()`)\n        updateValues.push(id)\n\n        await client.query(`\n          UPDATE prompts\n          SET ${updateFields.join(', ')}\n          WHERE id = $${paramIndex} AND deleted_at IS NULL\n        `, updateValues)\n      }\n\n      // 更新标签关联\n      if (data.tagIds !== undefined) {\n        // 删除现有标签关联\n        await client.query(`\n          DELETE FROM prompt_tags WHERE prompt_id = $1\n        `, [id])\n\n        // 添加新的标签关联\n        if (data.tagIds.length > 0) {\n          for (const tagId of data.tagIds) {\n            await client.query(`\n              INSERT INTO prompt_tags (prompt_id, tag_id)\n              VALUES ($1, $2)\n            `, [id, tagId])\n          }\n        }\n      }\n\n      // 清除缓存\n      invalidateClientCache([CLIENT_CACHE_KEYS.PROMPTS, `${CLIENT_CACHE_KEYS.PROMPT_DETAIL}_${id}`])\n\n      // 获取更新后的提示词信息\n      return await getPromptById(id) as PromptWithDetails\n    })\n  } catch (error) {\n    console.error('更新提示词失败:', error)\n    throw new Error('更新提示词失败')\n  }\n}\n\n/**\n * 删除提示词（软删除）\n */\nexport async function deletePrompt(id: string): Promise<void> {\n  try {\n    await query(`\n      UPDATE prompts\n      SET deleted_at = NOW(), updated_at = NOW()\n      WHERE id = $1 AND deleted_at IS NULL\n    `, [id])\n\n    // 清除缓存\n    invalidateClientCache([CLIENT_CACHE_KEYS.PROMPTS, `${CLIENT_CACHE_KEYS.PROMPT_DETAIL}_${id}`])\n  } catch (error) {\n    console.error('删除提示词失败:', error)\n    throw new Error('删除提示词失败')\n  }\n}\n\n/**\n * 增加使用次数\n */\nexport async function incrementUsageCount(id: string): Promise<void> {\n  try {\n    await query(`\n      UPDATE prompts\n      SET usage_count = usage_count + 1, updated_at = NOW()\n      WHERE id = $1 AND deleted_at IS NULL\n    `, [id])\n\n    // 清除相关缓存\n    invalidateClientCache([`${CLIENT_CACHE_KEYS.PROMPT_DETAIL}_${id}`, CLIENT_CACHE_KEYS.PROMPTS])\n  } catch (error) {\n    console.error('增加使用次数失败:', error)\n    throw new Error('增加使用次数失败')\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;;;;;AAcO,eAAe,WAAW,SAAuB,CAAC,CAAC;IACxD,MAAM,EACJ,OAAO,WAAW,EAClB,UAAU,EACV,SAAS,EAAE,EACX,SAAS,YAAY,EACrB,YAAY,MAAM,EAClB,QAAQ,EAAE,EACV,SAAS,CAAC,EACX,GAAG;IAEJ,wBAAwB;IACxB,MAAM,cAAc,CAAC,eAAe,WAAW,KAAK,SAAS;IAE7D,IAAI,aAAa;QACf,OAAO,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EACnB,wHAAA,CAAA,oBAAiB,CAAC,OAAO,EACzB,wHAAA,CAAA,mBAAgB,CAAC,MAAM,EACvB,IAAM,aAAa,SACnB;IAEJ,OAAO;QACL,OAAO,aAAa;IACtB;AACF;AAEA,eAAe,aAAa,MAAoB;IAC9C,MAAM,EACJ,OAAO,WAAW,EAClB,UAAU,EACV,SAAS,EAAE,EACX,SAAS,YAAY,EACrB,YAAY,MAAM,EAClB,QAAQ,EAAE,EACV,SAAS,CAAC,EACX,GAAG;IAEJ,IAAI;QACF,SAAS;QACT,IAAI,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+BjB,CAAC;QAED,MAAM,cAAqB,EAAE;QAC7B,IAAI,aAAa;QAEjB,SAAS;QACT,IAAI,YAAY;YACd,aAAa,CAAC,sBAAsB,EAAE,YAAY;YAClD,YAAY,IAAI,CAAC;YACjB;QACF;QAEA,SAAS;QACT,IAAI,aAAa;YACf,aAAa,CAAC;uBACG,EAAE,WAAW;4BACR,EAAE,WAAW;8FACqD,EAAE,aAAa,EAAE;OACxG,CAAC;YACF,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,EAAE;YACrC,cAAc;QAChB;QAEA,SAAS;QACT,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,aAAa,CAAC;;;+BAGW,EAAE,WAAW;OACrC,CAAC;YACF,YAAY,IAAI,CAAC;YACjB;QACF;QAEA,aAAa,CAAC,uCAAuC,CAAC;QAEtD,OAAO;QACP,MAAM,mBAAmB;YAAC;YAAc;YAAc;YAAe;SAAQ;QAC7E,MAAM,kBAAkB;YAAC;YAAO;SAAO;QAEvC,IAAI,iBAAiB,QAAQ,CAAC,WAAW,gBAAgB,QAAQ,CAAC,YAAY;YAC5E,aAAa,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE,UAAU,WAAW,IAAI;QACjE,OAAO;YACL,aAAa,CAAC,2BAA2B,CAAC;QAC5C;QAEA,OAAO;QACP,aAAa,CAAC,QAAQ,EAAE,WAAW,SAAS,EAAE,aAAa,GAAG;QAC9D,YAAY,IAAI,CAAC,OAAO;QAExB,OAAO;QACP,MAAM,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,WAAW;QAEtC,OAAO;QACP,IAAI,aAAa,CAAC;;;;;IAKlB,CAAC;QAED,MAAM,cAAqB,EAAE;QAC7B,IAAI,kBAAkB;QAEtB,IAAI,YAAY;YACd,cAAc,CAAC,sBAAsB,EAAE,iBAAiB;YACxD,YAAY,IAAI,CAAC;YACjB;QACF;QAEA,IAAI,aAAa;YACf,cAAc,CAAC;uBACE,EAAE,gBAAgB;4BACb,EAAE,gBAAgB;8FACgD,EAAE,kBAAkB,EAAE;OAC7G,CAAC;YACF,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,EAAE;YACrC,mBAAmB;QACrB;QAEA,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,cAAc,CAAC;;;+BAGU,EAAE,gBAAgB;OAC1C,CAAC;YACF,YAAY,IAAI,CAAC;QACnB;QAEA,MAAM,cAAc,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,YAAY;QAC5C,MAAM,QAAQ,SAAS,YAAY,IAAI,CAAC,EAAE,CAAC,KAAK;QAEhD,SAAS;QACT,MAAM,UAAU,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;gBAC7C,IAAI,IAAI,EAAE;gBACV,OAAO,IAAI,KAAK;gBAChB,aAAa,IAAI,WAAW;gBAC5B,SAAS,IAAI,OAAO;gBACpB,aAAa,IAAI,WAAW;gBAC5B,aAAa,IAAI,WAAW;gBAC5B,SAAS,IAAI,OAAO;gBACpB,UAAU,IAAI,QAAQ;gBACtB,YAAY,IAAI,UAAU;gBAC1B,YAAY,IAAI,UAAU;gBAC1B,YAAY,IAAI,UAAU;gBAC1B,UAAU,IAAI,aAAa,GAAG;oBAC5B,IAAI,IAAI,WAAW;oBACnB,MAAM,IAAI,aAAa;oBACvB,OAAO,IAAI,cAAc;oBACzB,MAAM,IAAI,aAAa;gBACzB,IAAI;gBACJ,MAAM,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,MAAc,QAAkB,CAAC;wBACxD,IAAI,IAAI,OAAO,CAAC,MAAM;wBACtB;wBACA,OAAO,IAAI,UAAU,CAAC,MAAM;oBAC9B,CAAC,GAAG,MAAM,CAAC,CAAC,MAAa,IAAI,IAAI;YACnC,CAAC;QAED,OAAO;YACL,MAAM;YACN;YACA,SAAS,SAAS,QAAQ;YAC1B,YAAY,SAAS,QAAQ,QAAQ,SAAS,QAAQ;QACxD;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,cAAc,EAAU;IAC5C,IAAI;QACF,kBAAkB;QAClB,IAAI,GAAG,UAAU,CAAC,WAAW;YAC3B,QAAQ,IAAI,CAAC,kBAAkB;YAC/B,OAAO;QACT;QAEA,mBAAmB;QACnB,OAAO,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EACnB,GAAG,wHAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI,EAC1C,wHAAA,CAAA,mBAAgB,CAAC,MAAM,EACvB;YACE,MAAM,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAgC5B,CAAC,EAAE;gBAAC;aAAG;YAEP,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,GAAG;gBAC5B,OAAO;YACT;YAEA,MAAM,MAAM,OAAO,IAAI,CAAC,EAAE;YAC1B,OAAO;gBACL,IAAI,IAAI,EAAE;gBACV,OAAO,IAAI,KAAK;gBAChB,aAAa,IAAI,WAAW;gBAC5B,SAAS,IAAI,OAAO;gBACpB,aAAa,IAAI,WAAW;gBAC5B,aAAa,IAAI,WAAW;gBAC5B,SAAS,IAAI,OAAO;gBACpB,UAAU,IAAI,QAAQ;gBACtB,YAAY,IAAI,UAAU;gBAC1B,YAAY,IAAI,UAAU;gBAC1B,YAAY,IAAI,UAAU;gBAC1B,UAAU,IAAI,aAAa,GAAG;oBAC5B,IAAI,IAAI,WAAW;oBACnB,MAAM,IAAI,aAAa;oBACvB,OAAO,IAAI,cAAc;oBACzB,MAAM,IAAI,aAAa;gBACzB,IAAI;gBACJ,MAAM,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,MAAc,QAAkB,CAAC;wBACxD,IAAI,IAAI,OAAO,CAAC,MAAM;wBACtB;wBACA,OAAO,IAAI,UAAU,CAAC,MAAM;oBAC9B,CAAC,GAAG,MAAM,CAAC,CAAC,MAAa,IAAI,IAAI;YACnC;QACF;IAEJ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,aAAa,IAAkB;IACnD,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE,OAAO;YAC9B,+BAA+B;YAC/B,MAAM,gBAAgB;YACtB,MAAM,eAAe,MAAM,OAAO,KAAK,CAAC,CAAC;;;;MAIzC,CAAC,EAAE;gBAAC,KAAK,KAAK;gBAAE,KAAK,WAAW;gBAAE,KAAK,OAAO;gBAAE,KAAK,WAAW;gBAAE,KAAK,OAAO,IAAI;gBAAe,KAAK,QAAQ,IAAI,CAAC;aAAE;YAErH,MAAM,SAAS,aAAa,IAAI,CAAC,EAAE;YAEnC,SAAS;YACT,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,GAAG;gBACzC,KAAK,MAAM,SAAS,KAAK,MAAM,CAAE;oBAC/B,MAAM,OAAO,KAAK,CAAC,CAAC;;;;UAIpB,CAAC,EAAE;wBAAC,OAAO,EAAE;wBAAE;qBAAM;gBACvB;YACF;YAEA,OAAO;YACP,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD,EAAE;gBAAC,wHAAA,CAAA,oBAAiB,CAAC,OAAO;aAAC;YAEjD,aAAa;YACb,OAAO,MAAM,cAAc,OAAO,EAAE;QACtC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,aAAa,EAAU,EAAE,IAAkB;IAC/D,IAAI;QACF,OAAO,MAAM,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE,OAAO;YAC9B,YAAY;YACZ,MAAM,eAAyB,EAAE;YACjC,MAAM,eAAsB,EAAE;YAC9B,IAAI,aAAa;YAEjB,IAAI,KAAK,KAAK,KAAK,WAAW;gBAC5B,aAAa,IAAI,CAAC,CAAC,SAAS,EAAE,YAAY;gBAC1C,aAAa,IAAI,CAAC,KAAK,KAAK;gBAC5B;YACF;YAEA,IAAI,KAAK,WAAW,KAAK,WAAW;gBAClC,aAAa,IAAI,CAAC,CAAC,eAAe,EAAE,YAAY;gBAChD,aAAa,IAAI,CAAC,KAAK,WAAW;gBAClC;YACF;YAEA,IAAI,KAAK,OAAO,KAAK,WAAW;gBAC9B,aAAa,IAAI,CAAC,CAAC,WAAW,EAAE,YAAY;gBAC5C,aAAa,IAAI,CAAC,KAAK,OAAO;gBAC9B;YACF;YAEA,IAAI,KAAK,WAAW,KAAK,WAAW;gBAClC,aAAa,IAAI,CAAC,CAAC,eAAe,EAAE,YAAY;gBAChD,aAAa,IAAI,CAAC,KAAK,WAAW;gBAClC;YACF;YAEA,IAAI,KAAK,QAAQ,KAAK,WAAW;gBAC/B,aAAa,IAAI,CAAC,CAAC,YAAY,EAAE,YAAY;gBAC7C,aAAa,IAAI,CAAC,KAAK,QAAQ;gBAC/B;YACF;YAEA,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,aAAa,IAAI,CAAC,CAAC,kBAAkB,CAAC;gBACtC,aAAa,IAAI,CAAC;gBAElB,MAAM,OAAO,KAAK,CAAC,CAAC;;cAEd,EAAE,aAAa,IAAI,CAAC,MAAM;sBAClB,EAAE,WAAW;QAC3B,CAAC,EAAE;YACL;YAEA,SAAS;YACT,IAAI,KAAK,MAAM,KAAK,WAAW;gBAC7B,WAAW;gBACX,MAAM,OAAO,KAAK,CAAC,CAAC;;QAEpB,CAAC,EAAE;oBAAC;iBAAG;gBAEP,WAAW;gBACX,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,GAAG;oBAC1B,KAAK,MAAM,SAAS,KAAK,MAAM,CAAE;wBAC/B,MAAM,OAAO,KAAK,CAAC,CAAC;;;YAGpB,CAAC,EAAE;4BAAC;4BAAI;yBAAM;oBAChB;gBACF;YACF;YAEA,OAAO;YACP,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD,EAAE;gBAAC,wHAAA,CAAA,oBAAiB,CAAC,OAAO;gBAAE,GAAG,wHAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI;aAAC;YAE7F,cAAc;YACd,OAAO,MAAM,cAAc;QAC7B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,aAAa,EAAU;IAC3C,IAAI;QACF,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;IAIb,CAAC,EAAE;YAAC;SAAG;QAEP,OAAO;QACP,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD,EAAE;YAAC,wHAAA,CAAA,oBAAiB,CAAC,OAAO;YAAE,GAAG,wHAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI;SAAC;IAC/F,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,oBAAoB,EAAU;IAClD,IAAI;QACF,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;IAIb,CAAC,EAAE;YAAC;SAAG;QAEP,SAAS;QACT,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD,EAAE;YAAC,GAAG,wHAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,CAAC,EAAE,IAAI;YAAE,wHAAA,CAAA,oBAAiB,CAAC,OAAO;SAAC;IAC/F,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/app/api/prompts/%5Bid%5D/usage/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { incrementUsageCount } from '@/lib/database/prompts'\n\n// POST /api/prompts/[id]/usage - 增加使用次数\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    await incrementUsageCount(params.id)\n    return NextResponse.json({ success: true })\n  } catch (error) {\n    console.error('增加使用次数失败:', error)\n    return NextResponse.json(\n      { error: '增加使用次数失败' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;;;;AAGO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,EAAE;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}