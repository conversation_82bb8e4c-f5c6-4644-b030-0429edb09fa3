{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/database/client.ts"], "sourcesContent": ["import { Pool, PoolClient } from 'pg'\n\n// 数据库连接配置\nconst dbConfig = {\n  host: process.env.DATABASE_HOST || '*************',\n  port: parseInt(process.env.DATABASE_PORT || '5432'),\n  database: process.env.DATABASE_NAME || 'prompt',\n  user: process.env.DATABASE_USER || 'Seven',\n  password: process.env.DATABASE_PASSWORD || 'Abc112211',\n  // 连接池配置\n  max: 20, // 最大连接数\n  idleTimeoutMillis: 30000, // 空闲连接超时时间\n  connectionTimeoutMillis: 2000, // 连接超时时间\n}\n\n// 创建连接池\nlet pool: Pool | null = null\n\n/**\n * 获取数据库连接池\n */\nexport function getPool(): Pool {\n  if (!pool) {\n    pool = new Pool(dbConfig)\n    \n    // 监听连接池事件\n    pool.on('error', (err) => {\n      console.error('数据库连接池错误:', err)\n    })\n    \n    pool.on('connect', () => {\n      console.log('数据库连接成功')\n    })\n  }\n  \n  return pool\n}\n\n/**\n * 获取数据库连接\n */\nexport async function getConnection(): Promise<PoolClient> {\n  const pool = getPool()\n  return await pool.connect()\n}\n\n/**\n * 执行查询\n */\nexport async function query(text: string, params?: any[]): Promise<any> {\n  const pool = getPool()\n  try {\n    const result = await pool.query(text, params)\n    return result\n  } catch (error) {\n    console.error('数据库查询错误:', error)\n    throw error\n  }\n}\n\n/**\n * 执行事务\n */\nexport async function transaction<T>(\n  callback: (client: PoolClient) => Promise<T>\n): Promise<T> {\n  const client = await getConnection()\n  \n  try {\n    await client.query('BEGIN')\n    const result = await callback(client)\n    await client.query('COMMIT')\n    return result\n  } catch (error) {\n    await client.query('ROLLBACK')\n    throw error\n  } finally {\n    client.release()\n  }\n}\n\n/**\n * 关闭连接池\n */\nexport async function closePool(): Promise<void> {\n  if (pool) {\n    await pool.end()\n    pool = null\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;AAEA,UAAU;AACV,MAAM,WAAW;IACf,MAAM,QAAQ,GAAG,CAAC,aAAa,IAAI;IACnC,MAAM,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;IAC5C,UAAU,QAAQ,GAAG,CAAC,aAAa,IAAI;IACvC,MAAM,QAAQ,GAAG,CAAC,aAAa,IAAI;IACnC,UAAU,QAAQ,GAAG,CAAC,iBAAiB,IAAI;IAC3C,QAAQ;IACR,KAAK;IACL,mBAAmB;IACnB,yBAAyB;AAC3B;AAEA,QAAQ;AACR,IAAI,OAAoB;AAKjB,SAAS;IACd,IAAI,CAAC,MAAM;QACT,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;QAEhB,UAAU;QACV,KAAK,EAAE,CAAC,SAAS,CAAC;YAChB,QAAQ,KAAK,CAAC,aAAa;QAC7B;QAEA,KAAK,EAAE,CAAC,WAAW;YACjB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,OAAO;AACT;AAKO,eAAe;IACpB,MAAM,OAAO;IACb,OAAO,MAAM,KAAK,OAAO;AAC3B;AAKO,eAAe,MAAM,IAAY,EAAE,MAAc;IACtD,MAAM,OAAO;IACb,IAAI;QACF,MAAM,SAAS,MAAM,KAAK,KAAK,CAAC,MAAM;QACtC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,YAAY;QAC1B,MAAM;IACR;AACF;AAKO,eAAe,YACpB,QAA4C;IAE5C,MAAM,SAAS,MAAM;IAErB,IAAI;QACF,MAAM,OAAO,KAAK,CAAC;QACnB,MAAM,SAAS,MAAM,SAAS;QAC9B,MAAM,OAAO,KAAK,CAAC;QACnB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,OAAO,KAAK,CAAC;QACnB,MAAM;IACR,SAAU;QACR,OAAO,OAAO;IAChB;AACF;AAKO,eAAe;IACpB,IAAI,MAAM;QACR,MAAM,KAAK,GAAG;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/client-cache.ts"], "sourcesContent": ["/**\n * 浏览器端缓存系统\n * 解决服务器端内存缓存在 Vercel 等 serverless 环境中的问题\n */\n\ninterface CacheItem<T> {\n  data: T\n  timestamp: number\n  ttl: number\n  version: string\n}\n\nclass ClientCache {\n  private memoryCache = new Map<string, CacheItem<any>>()\n  private readonly version = '1.0.0' // 缓存版本，用于缓存失效\n\n  /**\n   * 生成稳定的缓存键\n   */\n  private generateKey(key: string, params?: any): string {\n    if (!params) return key\n    \n    // 确保对象键的顺序一致\n    const sortedParams = this.sortObject(params)\n    return `${key}:${JSON.stringify(sortedParams)}`\n  }\n\n  /**\n   * 递归排序对象键，确保缓存键的一致性\n   */\n  private sortObject(obj: any): any {\n    if (obj === null || typeof obj !== 'object') return obj\n    if (Array.isArray(obj)) return obj.map(item => this.sortObject(item))\n    \n    const sorted: any = {}\n    Object.keys(obj).sort().forEach(key => {\n      sorted[key] = this.sortObject(obj[key])\n    })\n    return sorted\n  }\n\n  /**\n   * 设置缓存（内存 + localStorage）\n   */\n  set<T>(key: string, data: T, ttl: number = 5 * 60 * 1000, params?: any): void {\n    const cacheKey = this.generateKey(key, params)\n    const item: CacheItem<T> = {\n      data,\n      timestamp: Date.now(),\n      ttl,\n      version: this.version\n    }\n\n    // 内存缓存\n    this.memoryCache.set(cacheKey, item)\n\n    // localStorage 缓存（仅在浏览器环境）\n    if (typeof window !== 'undefined') {\n      try {\n        localStorage.setItem(`cache_${cacheKey}`, JSON.stringify(item))\n      } catch (error) {\n        console.warn('localStorage 缓存失败:', error)\n      }\n    }\n  }\n\n  /**\n   * 获取缓存\n   */\n  get<T>(key: string, params?: any): T | null {\n    const cacheKey = this.generateKey(key, params)\n\n    // 先尝试内存缓存\n    let item = this.memoryCache.get(cacheKey)\n\n    // 如果内存缓存没有，尝试 localStorage\n    if (!item && typeof window !== 'undefined') {\n      try {\n        const stored = localStorage.getItem(`cache_${cacheKey}`)\n        if (stored) {\n          item = JSON.parse(stored)\n          // 恢复到内存缓存\n          if (item) {\n            this.memoryCache.set(cacheKey, item)\n          }\n        }\n      } catch (error) {\n        console.warn('localStorage 读取失败:', error)\n      }\n    }\n\n    if (!item) return null\n\n    // 检查版本\n    if (item.version !== this.version) {\n      this.delete(key, params)\n      return null\n    }\n\n    // 检查是否过期\n    if (Date.now() - item.timestamp > item.ttl) {\n      this.delete(key, params)\n      return null\n    }\n\n    return item.data as T\n  }\n\n  /**\n   * 删除缓存\n   */\n  delete(key: string, params?: any): void {\n    const cacheKey = this.generateKey(key, params)\n    \n    this.memoryCache.delete(cacheKey)\n    \n    if (typeof window !== 'undefined') {\n      localStorage.removeItem(`cache_${cacheKey}`)\n    }\n  }\n\n  /**\n   * 清除所有缓存\n   */\n  clear(): void {\n    this.memoryCache.clear()\n    \n    if (typeof window !== 'undefined') {\n      const keys = Object.keys(localStorage)\n      keys.forEach(key => {\n        if (key.startsWith('cache_')) {\n          localStorage.removeItem(key)\n        }\n      })\n    }\n  }\n\n  /**\n   * 清除匹配模式的缓存\n   */\n  clearPattern(pattern: string): void {\n    // 清除内存缓存\n    for (const key of this.memoryCache.keys()) {\n      if (key.includes(pattern)) {\n        this.memoryCache.delete(key)\n      }\n    }\n\n    // 清除 localStorage 缓存\n    if (typeof window !== 'undefined') {\n      const keys = Object.keys(localStorage)\n      keys.forEach(key => {\n        if (key.startsWith('cache_') && key.includes(pattern)) {\n          localStorage.removeItem(key)\n        }\n      })\n    }\n  }\n\n  /**\n   * 获取缓存统计信息\n   */\n  getStats(): { memorySize: number; localStorageSize: number; keys: string[] } {\n    let localStorageSize = 0\n    const keys: string[] = []\n\n    if (typeof window !== 'undefined') {\n      Object.keys(localStorage).forEach(key => {\n        if (key.startsWith('cache_')) {\n          localStorageSize++\n          keys.push(key.replace('cache_', ''))\n        }\n      })\n    }\n\n    return {\n      memorySize: this.memoryCache.size,\n      localStorageSize,\n      keys: Array.from(new Set([...Array.from(this.memoryCache.keys()), ...keys]))\n    }\n  }\n}\n\n// 创建全局客户端缓存实例\nexport const clientCache = new ClientCache()\n\n// 缓存键常量\nexport const CLIENT_CACHE_KEYS = {\n  CATEGORIES: 'categories',\n  PROMPTS: 'prompts',\n  TAGS: 'tags',\n  SEARCH_HISTORY: 'search_history',\n  PROMPT_DETAIL: 'prompt_detail',\n  APP_PREFERENCES: 'app_preferences',\n} as const\n\n// 缓存时间常量（毫秒）\nexport const CLIENT_CACHE_TTL = {\n  SHORT: 30 * 1000,        // 30秒\n  MEDIUM: 2 * 60 * 1000,   // 2分钟\n  LONG: 10 * 60 * 1000,    // 10分钟\n  VERY_LONG: 30 * 60 * 1000, // 30分钟\n} as const\n\n/**\n * 客户端缓存装饰器函数\n */\nexport async function withClientCache<T>(\n  cacheKey: string,\n  ttl: number,\n  fn: () => Promise<T>,\n  params?: any\n): Promise<T> {\n  // 尝试从缓存获取\n  const cached = clientCache.get<T>(cacheKey, params)\n  if (cached !== null) {\n    console.log(`Client cache hit: ${cacheKey}`, params)\n    return cached\n  }\n\n  // 缓存未命中，执行函数\n  console.log(`Client cache miss: ${cacheKey}`, params)\n  const result = await fn()\n  \n  // 存入缓存\n  clientCache.set(cacheKey, result, ttl, params)\n  \n  return result\n}\n\n/**\n * 清除相关缓存\n */\nexport function invalidateClientCache(patterns: string[]): void {\n  patterns.forEach(pattern => {\n    clientCache.clearPattern(pattern)\n  })\n  console.log(`Invalidated client cache patterns: ${patterns.join(', ')}`)\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AASD,MAAM;IACI,cAAc,IAAI,MAA6B;IACtC,UAAU,QAAQ,cAAc;KAAf;IAElC;;GAEC,GACD,AAAQ,YAAY,GAAW,EAAE,MAAY,EAAU;QACrD,IAAI,CAAC,QAAQ,OAAO;QAEpB,aAAa;QACb,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC;QACrC,OAAO,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,eAAe;IACjD;IAEA;;GAEC,GACD,AAAQ,WAAW,GAAQ,EAAO;QAChC,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU,OAAO;QACpD,IAAI,MAAM,OAAO,CAAC,MAAM,OAAO,IAAI,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,UAAU,CAAC;QAE/D,MAAM,SAAc,CAAC;QACrB,OAAO,IAAI,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,CAAA;YAC9B,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI;QACxC;QACA,OAAO;IACT;IAEA;;GAEC,GACD,IAAO,GAAW,EAAE,IAAO,EAAE,MAAc,IAAI,KAAK,IAAI,EAAE,MAAY,EAAQ;QAC5E,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,KAAK;QACvC,MAAM,OAAqB;YACzB;YACA,WAAW,KAAK,GAAG;YACnB;YACA,SAAS,IAAI,CAAC,OAAO;QACvB;QAEA,OAAO;QACP,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU;QAE/B,2BAA2B;QAC3B;;IAOF;IAEA;;GAEC,GACD,IAAO,GAAW,EAAE,MAAY,EAAY;QAC1C,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,KAAK;QAEvC,UAAU;QACV,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QAEhC,2BAA2B;QAC3B;;QAeA,IAAI,CAAC,MAAM,OAAO;QAElB,OAAO;QACP,IAAI,KAAK,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;YACjC,IAAI,CAAC,MAAM,CAAC,KAAK;YACjB,OAAO;QACT;QAEA,SAAS;QACT,IAAI,KAAK,GAAG,KAAK,KAAK,SAAS,GAAG,KAAK,GAAG,EAAE;YAC1C,IAAI,CAAC,MAAM,CAAC,KAAK;YACjB,OAAO;QACT;QAEA,OAAO,KAAK,IAAI;IAClB;IAEA;;GAEC,GACD,OAAO,GAAW,EAAE,MAAY,EAAQ;QACtC,MAAM,WAAW,IAAI,CAAC,WAAW,CAAC,KAAK;QAEvC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QAExB;;IAGF;IAEA;;GAEC,GACD,QAAc;QACZ,IAAI,CAAC,WAAW,CAAC,KAAK;QAEtB;;IAQF;IAEA;;GAEC,GACD,aAAa,OAAe,EAAQ;QAClC,SAAS;QACT,KAAK,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,GAAI;YACzC,IAAI,IAAI,QAAQ,CAAC,UAAU;gBACzB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YAC1B;QACF;QAEA,qBAAqB;QACrB;;IAQF;IAEA;;GAEC,GACD,WAA6E;QAC3E,IAAI,mBAAmB;QACvB,MAAM,OAAiB,EAAE;QAEzB;;QASA,OAAO;YACL,YAAY,IAAI,CAAC,WAAW,CAAC,IAAI;YACjC;YACA,MAAM,MAAM,IAAI,CAAC,IAAI,IAAI;mBAAI,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI;mBAAQ;aAAK;QAC5E;IACF;AACF;AAGO,MAAM,cAAc,IAAI;AAGxB,MAAM,oBAAoB;IAC/B,YAAY;IACZ,SAAS;IACT,MAAM;IACN,gBAAgB;IAChB,eAAe;IACf,iBAAiB;AACnB;AAGO,MAAM,mBAAmB;IAC9B,OAAO,KAAK;IACZ,QAAQ,IAAI,KAAK;IACjB,MAAM,KAAK,KAAK;IAChB,WAAW,KAAK,KAAK;AACvB;AAKO,eAAe,gBACpB,QAAgB,EAChB,GAAW,EACX,EAAoB,EACpB,MAAY;IAEZ,UAAU;IACV,MAAM,SAAS,YAAY,GAAG,CAAI,UAAU;IAC5C,IAAI,WAAW,MAAM;QACnB,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU,EAAE;QAC7C,OAAO;IACT;IAEA,aAAa;IACb,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU,EAAE;IAC9C,MAAM,SAAS,MAAM;IAErB,OAAO;IACP,YAAY,GAAG,CAAC,UAAU,QAAQ,KAAK;IAEvC,OAAO;AACT;AAKO,SAAS,sBAAsB,QAAkB;IACtD,SAAS,OAAO,CAAC,CAAA;QACf,YAAY,YAAY,CAAC;IAC3B;IACA,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,SAAS,IAAI,CAAC,OAAO;AACzE", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/lib/database/categories.ts"], "sourcesContent": ["import { query, transaction } from '@/lib/database/client'\nimport { withC<PERSON>Cache, CLIENT_CACHE_KEYS, CLIENT_CACHE_TTL, invalidateClientCache } from '@/lib/client-cache'\nimport type {\n  Category,\n  CategoryInsert,\n  CategoryUpdate,\n  CategoryWithCount,\n  DatabaseError\n} from '@/types/database'\n\n/**\n * 获取所有分类（包含提示词数量）\n */\nexport async function getCategories(): Promise<CategoryWithCount[]> {\n  return withClientCache(\n    CLIENT_CACHE_KEYS.CATEGORIES,\n    CLIENT_CACHE_TTL.LONG, // 10分钟客户端缓存\n    async () => {\n      try {\n        const result = await query(`\n          SELECT \n            c.id,\n            c.name,\n            c.description,\n            c.color,\n            c.icon,\n            c.sort_order,\n            c.created_at,\n            c.updated_at,\n            COUNT(p.id) as prompt_count\n          FROM categories c\n          LEFT JOIN prompts p ON c.id = p.category_id AND p.deleted_at IS NULL\n          WHERE c.deleted_at IS NULL\n          GROUP BY c.id, c.name, c.description, c.color, c.icon, c.sort_order, c.created_at, c.updated_at\n          ORDER BY c.sort_order, c.name\n        `)\n\n        return result.rows.map((row: any) => ({\n          id: row.id,\n          name: row.name,\n          description: row.description,\n          color: row.color,\n          icon: row.icon,\n          sort_order: row.sort_order,\n          created_at: row.created_at,\n          updated_at: row.updated_at,\n          deleted_at: row.deleted_at,\n          prompt_count: parseInt(row.prompt_count)\n        }))\n      } catch (error) {\n        console.error('获取分类失败:', error)\n        // 网络错误或其他问题时返回空数组，避免阻塞整个应用\n        return []\n      }\n    }\n  )\n}\n\n/**\n * 根据ID获取单个分类\n */\nexport async function getCategoryById(id: string): Promise<Category | null> {\n  try {\n    const result = await query(`\n      SELECT * FROM categories \n      WHERE id = $1 AND deleted_at IS NULL\n    `, [id])\n\n    if (result.rows.length === 0) {\n      return null\n    }\n\n    const row = result.rows[0]\n    return {\n      id: row.id,\n      name: row.name,\n      description: row.description,\n      color: row.color,\n      icon: row.icon,\n      sortOrder: row.sort_order,\n      createdAt: row.created_at,\n      updatedAt: row.updated_at\n    }\n  } catch (error) {\n    console.error('获取分类失败:', error)\n    throw new Error('获取分类失败')\n  }\n}\n\n/**\n * 创建新分类\n */\nexport async function createCategory(data: CategoryInsert): Promise<Category> {\n  try {\n    const result = await query(`\n      INSERT INTO categories (name, description, color, icon, sort_order)\n      VALUES ($1, $2, $3, $4, $5)\n      RETURNING *\n    `, [data.name, data.description, data.color, data.icon, data.sortOrder || 0])\n\n    // 清除缓存\n    invalidateClientCache([CLIENT_CACHE_KEYS.CATEGORIES])\n\n    const row = result.rows[0]\n    return {\n      id: row.id,\n      name: row.name,\n      description: row.description,\n      color: row.color,\n      icon: row.icon,\n      sortOrder: row.sort_order,\n      createdAt: row.created_at,\n      updatedAt: row.updated_at\n    }\n  } catch (error) {\n    console.error('创建分类失败:', error)\n    if (error instanceof Error && error.message.includes('duplicate key')) {\n      throw new Error('分类名称已存在')\n    }\n    throw new Error('创建分类失败')\n  }\n}\n\n/**\n * 更新分类\n */\nexport async function updateCategory(id: string, data: CategoryUpdate): Promise<Category> {\n  try {\n    const updateFields: string[] = []\n    const updateValues: any[] = []\n    let paramIndex = 1\n\n    if (data.name !== undefined) {\n      updateFields.push(`name = $${paramIndex}`)\n      updateValues.push(data.name)\n      paramIndex++\n    }\n\n    if (data.description !== undefined) {\n      updateFields.push(`description = $${paramIndex}`)\n      updateValues.push(data.description)\n      paramIndex++\n    }\n\n    if (data.color !== undefined) {\n      updateFields.push(`color = $${paramIndex}`)\n      updateValues.push(data.color)\n      paramIndex++\n    }\n\n    if (data.icon !== undefined) {\n      updateFields.push(`icon = $${paramIndex}`)\n      updateValues.push(data.icon)\n      paramIndex++\n    }\n\n    if (data.sortOrder !== undefined) {\n      updateFields.push(`sort_order = $${paramIndex}`)\n      updateValues.push(data.sortOrder)\n      paramIndex++\n    }\n\n    if (updateFields.length === 0) {\n      throw new Error('没有要更新的字段')\n    }\n\n    updateFields.push(`updated_at = NOW()`)\n    updateValues.push(id)\n\n    const result = await query(`\n      UPDATE categories \n      SET ${updateFields.join(', ')}\n      WHERE id = $${paramIndex} AND deleted_at IS NULL\n      RETURNING *\n    `, updateValues)\n\n    if (result.rows.length === 0) {\n      throw new Error('分类不存在')\n    }\n\n    // 清除缓存\n    invalidateClientCache([CLIENT_CACHE_KEYS.CATEGORIES])\n\n    const row = result.rows[0]\n    return {\n      id: row.id,\n      name: row.name,\n      description: row.description,\n      color: row.color,\n      icon: row.icon,\n      sortOrder: row.sort_order,\n      createdAt: row.created_at,\n      updatedAt: row.updated_at\n    }\n  } catch (error) {\n    console.error('更新分类失败:', error)\n    if (error instanceof Error && error.message.includes('duplicate key')) {\n      throw new Error('分类名称已存在')\n    }\n    throw new Error('更新分类失败')\n  }\n}\n\n/**\n * 删除分类（软删除）\n */\nexport async function deleteCategory(id: string): Promise<void> {\n  try {\n    await transaction(async (client) => {\n      // 检查是否有提示词使用此分类\n      const promptResult = await client.query(`\n        SELECT COUNT(*) as count \n        FROM prompts \n        WHERE category_id = $1 AND deleted_at IS NULL\n      `, [id])\n\n      const promptCount = parseInt(promptResult.rows[0].count)\n      if (promptCount > 0) {\n        throw new Error(`无法删除分类，还有 ${promptCount} 个提示词使用此分类`)\n      }\n\n      // 软删除分类\n      const result = await client.query(`\n        UPDATE categories \n        SET deleted_at = NOW(), updated_at = NOW()\n        WHERE id = $1 AND deleted_at IS NULL\n        RETURNING id\n      `, [id])\n\n      if (result.rows.length === 0) {\n        throw new Error('分类不存在')\n      }\n    })\n\n    // 清除缓存\n    invalidateClientCache([CLIENT_CACHE_KEYS.CATEGORIES])\n  } catch (error) {\n    console.error('删除分类失败:', error)\n    throw error\n  }\n}\n\n/**\n * 批量更新分类排序\n */\nexport async function updateCategoriesOrder(categoryOrders: { id: string; sortOrder: number }[]): Promise<void> {\n  try {\n    await transaction(async (client) => {\n      for (const { id, sortOrder } of categoryOrders) {\n        await client.query(`\n          UPDATE categories \n          SET sort_order = $1, updated_at = NOW()\n          WHERE id = $2 AND deleted_at IS NULL\n        `, [sortOrder, id])\n      }\n    })\n\n    // 清除缓存\n    invalidateClientCache([CLIENT_CACHE_KEYS.CATEGORIES])\n  } catch (error) {\n    console.error('更新分类排序失败:', error)\n    throw new Error('更新分类排序失败')\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;;;;;AAYO,eAAe;IACpB,OAAO,CAAA,GAAA,wHAAA,CAAA,kBAAe,AAAD,EACnB,wHAAA,CAAA,oBAAiB,CAAC,UAAU,EAC5B,wHAAA,CAAA,mBAAgB,CAAC,IAAI,EACrB;QACE,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;;;;;;;;;;;;;QAgB5B,CAAC;YAED,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;oBACpC,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,IAAI;oBACd,aAAa,IAAI,WAAW;oBAC5B,OAAO,IAAI,KAAK;oBAChB,MAAM,IAAI,IAAI;oBACd,YAAY,IAAI,UAAU;oBAC1B,YAAY,IAAI,UAAU;oBAC1B,YAAY,IAAI,UAAU;oBAC1B,YAAY,IAAI,UAAU;oBAC1B,cAAc,SAAS,IAAI,YAAY;gBACzC,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,2BAA2B;YAC3B,OAAO,EAAE;QACX;IACF;AAEJ;AAKO,eAAe,gBAAgB,EAAU;IAC9C,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;IAG5B,CAAC,EAAE;YAAC;SAAG;QAEP,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,GAAG;YAC5B,OAAO;QACT;QAEA,MAAM,MAAM,OAAO,IAAI,CAAC,EAAE;QAC1B,OAAO;YACL,IAAI,IAAI,EAAE;YACV,MAAM,IAAI,IAAI;YACd,aAAa,IAAI,WAAW;YAC5B,OAAO,IAAI,KAAK;YAChB,MAAM,IAAI,IAAI;YACd,WAAW,IAAI,UAAU;YACzB,WAAW,IAAI,UAAU;YACzB,WAAW,IAAI,UAAU;QAC3B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,eAAe,IAAoB;IACvD,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;;;IAI5B,CAAC,EAAE;YAAC,KAAK,IAAI;YAAE,KAAK,WAAW;YAAE,KAAK,KAAK;YAAE,KAAK,IAAI;YAAE,KAAK,SAAS,IAAI;SAAE;QAE5E,OAAO;QACP,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD,EAAE;YAAC,wHAAA,CAAA,oBAAiB,CAAC,UAAU;SAAC;QAEpD,MAAM,MAAM,OAAO,IAAI,CAAC,EAAE;QAC1B,OAAO;YACL,IAAI,IAAI,EAAE;YACV,MAAM,IAAI,IAAI;YACd,aAAa,IAAI,WAAW;YAC5B,OAAO,IAAI,KAAK;YAChB,MAAM,IAAI,IAAI;YACd,WAAW,IAAI,UAAU;YACzB,WAAW,IAAI,UAAU;YACzB,WAAW,IAAI,UAAU;QAC3B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,kBAAkB;YACrE,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,eAAe,EAAU,EAAE,IAAoB;IACnE,IAAI;QACF,MAAM,eAAyB,EAAE;QACjC,MAAM,eAAsB,EAAE;QAC9B,IAAI,aAAa;QAEjB,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,aAAa,IAAI,CAAC,CAAC,QAAQ,EAAE,YAAY;YACzC,aAAa,IAAI,CAAC,KAAK,IAAI;YAC3B;QACF;QAEA,IAAI,KAAK,WAAW,KAAK,WAAW;YAClC,aAAa,IAAI,CAAC,CAAC,eAAe,EAAE,YAAY;YAChD,aAAa,IAAI,CAAC,KAAK,WAAW;YAClC;QACF;QAEA,IAAI,KAAK,KAAK,KAAK,WAAW;YAC5B,aAAa,IAAI,CAAC,CAAC,SAAS,EAAE,YAAY;YAC1C,aAAa,IAAI,CAAC,KAAK,KAAK;YAC5B;QACF;QAEA,IAAI,KAAK,IAAI,KAAK,WAAW;YAC3B,aAAa,IAAI,CAAC,CAAC,QAAQ,EAAE,YAAY;YACzC,aAAa,IAAI,CAAC,KAAK,IAAI;YAC3B;QACF;QAEA,IAAI,KAAK,SAAS,KAAK,WAAW;YAChC,aAAa,IAAI,CAAC,CAAC,cAAc,EAAE,YAAY;YAC/C,aAAa,IAAI,CAAC,KAAK,SAAS;YAChC;QACF;QAEA,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,aAAa,IAAI,CAAC,CAAC,kBAAkB,CAAC;QACtC,aAAa,IAAI,CAAC;QAElB,MAAM,SAAS,MAAM,CAAA,GAAA,2HAAA,CAAA,QAAK,AAAD,EAAE,CAAC;;UAEtB,EAAE,aAAa,IAAI,CAAC,MAAM;kBAClB,EAAE,WAAW;;IAE3B,CAAC,EAAE;QAEH,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,GAAG;YAC5B,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;QACP,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD,EAAE;YAAC,wHAAA,CAAA,oBAAiB,CAAC,UAAU;SAAC;QAEpD,MAAM,MAAM,OAAO,IAAI,CAAC,EAAE;QAC1B,OAAO;YACL,IAAI,IAAI,EAAE;YACV,MAAM,IAAI,IAAI;YACd,aAAa,IAAI,WAAW;YAC5B,OAAO,IAAI,KAAK;YAChB,MAAM,IAAI,IAAI;YACd,WAAW,IAAI,UAAU;YACzB,WAAW,IAAI,UAAU;YACzB,WAAW,IAAI,UAAU;QAC3B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,kBAAkB;YACrE,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,eAAe,EAAU;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE,OAAO;YACvB,gBAAgB;YAChB,MAAM,eAAe,MAAM,OAAO,KAAK,CAAC,CAAC;;;;MAIzC,CAAC,EAAE;gBAAC;aAAG;YAEP,MAAM,cAAc,SAAS,aAAa,IAAI,CAAC,EAAE,CAAC,KAAK;YACvD,IAAI,cAAc,GAAG;gBACnB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,YAAY,UAAU,CAAC;YACtD;YAEA,QAAQ;YACR,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,CAAC;;;;;MAKnC,CAAC,EAAE;gBAAC;aAAG;YAEP,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,GAAG;gBAC5B,MAAM,IAAI,MAAM;YAClB;QACF;QAEA,OAAO;QACP,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD,EAAE;YAAC,wHAAA,CAAA,oBAAiB,CAAC,UAAU;SAAC;IACtD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,MAAM;IACR;AACF;AAKO,eAAe,sBAAsB,cAAmD;IAC7F,IAAI;QACF,MAAM,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE,OAAO;YACvB,KAAK,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,eAAgB;gBAC9C,MAAM,OAAO,KAAK,CAAC,CAAC;;;;QAIpB,CAAC,EAAE;oBAAC;oBAAW;iBAAG;YACpB;QACF;QAEA,OAAO;QACP,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD,EAAE;YAAC,wHAAA,CAAA,oBAAiB,CAAC,UAAU;SAAC;IACtD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Cursor%20Project/prompy%20augment%20-%20%E5%89%AF%E6%9C%AC/prompt/app/api/categories/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getCategoryById, updateCategory, deleteCategory } from '@/lib/database/categories'\nimport type { CategoryUpdate } from '@/types/database'\n\n// GET /api/categories/[id] - 获取单个分类\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const result = await getCategoryById(params.id)\n    if (!result) {\n      return NextResponse.json(\n        { error: '分类不存在' },\n        { status: 404 }\n      )\n    }\n    return NextResponse.json(result)\n  } catch (error) {\n    console.error('获取分类失败:', error)\n    return NextResponse.json(\n      { error: '获取分类失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// PUT /api/categories/[id] - 更新分类\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const data: CategoryUpdate = await request.json()\n    const result = await updateCategory(params.id, data)\n    return NextResponse.json(result)\n  } catch (error) {\n    console.error('更新分类失败:', error)\n    return NextResponse.json(\n      { error: error instanceof Error ? error.message : '更新分类失败' },\n      { status: 500 }\n    )\n  }\n}\n\n// DELETE /api/categories/[id] - 删除分类\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    await deleteCategory(params.id)\n    return NextResponse.json({ success: true })\n  } catch (error) {\n    console.error('删除分类失败:', error)\n    return NextResponse.json(\n      { error: error instanceof Error ? error.message : '删除分类失败' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;;;;AAIO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,EAAE;QAC9C,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAQ,GACjB;gBAAE,QAAQ;YAAI;QAElB;QACA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAS,GAClB;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,OAAuB,MAAM,QAAQ,IAAI;QAC/C,MAAM,SAAS,MAAM,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,EAAE,EAAE;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAS,GAC3D;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,EAAE;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAS,GAC3D;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}